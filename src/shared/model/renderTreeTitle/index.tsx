import {
  FileDoneOutlined,
  FileOutlined,
  FolderOutlined,
  FolderViewOutlined,
} from '@ant-design/icons';
import { Popover, Skeleton, Tooltip, Typography } from 'antd';
import classNames from 'classnames';
import type { ReactNode } from 'react';
import { TextIcon } from 'shared/ui';
import { RenderAdditionalTags } from './RenderAdiitionalTags';

import styles from './styles.module.scss';
/** @store renderTreeTitle */

export const renderTreeTitle = (
  node: TreeElement,
  isSelected?: boolean,
  showCustomFolders?: boolean,
  isLinked?: boolean,
  isKVIconVisible?: boolean,
): ReactNode => {
  if (node.isSkeleton) {
    return (
      <div style={{ width: `${node.width}%` || '60%' }}>
        <Skeleton active paragraph={false} className={styles.skeleton} />
      </div>
    );
  }

  const isMain = node.isMain === 1;
  const FolderIcon = showCustomFolders ? FolderViewOutlined : FolderOutlined;
  const FileIcon = isMain ? FileDoneOutlined : FileOutlined;

  return (
    <div
      key={`renderTitle-${node.itemId}${node.key}`}
      style={{ backgroundColor: node.tagged ? '#08FD9033' : 'inherit' }}
      className={classNames(styles.container, isLinked && styles.linked)}
    >
      {isKVIconVisible ? (
        <Tooltip
          title={
            node.cabinetOnly
              ? `${node.isDirectory ? 'Директория' : 'Файл'} скрыт${
                  node.isDirectory ? 'а' : ''
                } для Контроля выполнения`
              : `${node.isDirectory ? 'Директория' : 'Файл'} доступ${
                  node.isDirectory ? 'на' : 'ен'
                } для Контроля выполнения`
          }
        >
          <TextIcon text="КВ" color="#1890FF" disabled={node.cabinetOnly!} />
        </Tooltip>
      ) : null}
      {node.isDirectory ? (
        <FolderIcon
          key={`render${showCustomFolders ? 'Linked' : ''}Folder-${
            node.itemId
          }${node.key}`}
          style={{
            color:
              node.isFixed === 1 || node.isFixed === 3
                ? '#1890ff'
                : node.isFixed === 2
                ? '#ff4d4f'
                : 'inherit',
          }}
        />
      ) : (
        <FileIcon
          key={`render${isMain ? 'Main' : ''}Folder-${node.itemId}${node.key}`}
        />
      )}
      <Popover
        trigger={
          Object.hasOwn(node, 'questions') || Object.hasOwn(node, 'systemHint')
            ? ['hover']
            : []
        }
        content={
          <RenderAdditionalTags
            questions={node.questions || []}
            systemHint={node.systemHint || ''}
          />
        }
        placement="left"
      >
        <Tooltip title={node.title}>
          <Typography.Text
            title=""
            key={`renderTitleNode-${node.itemId}${node.key}`}
            className={classNames(styles.title)}
            style={{
              ...(isSelected && { fontWeight: 700 }),
            }}
          >
            {`${node.title}${
              (node?.totalCountOfLeafs ?? -1) >= 0
                ? ` [${node.totalCountOfLeafs}]`
                : ''
            }`}
          </Typography.Text>
        </Tooltip>
      </Popover>
    </div>
  );
};
