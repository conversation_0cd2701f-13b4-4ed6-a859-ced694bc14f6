import { useCallback, useRef, useState, useEffect } from 'react';

interface PerformanceMetrics {
  operationType: 'select' | 'selectAll' | 'clear';
  duration: number;
  itemsCount: number;
  timestamp: number;
}

interface PerformanceStats {
  averageSelectTime: number;
  averageSelectAllTime: number;
  averageClearTime: number;
  totalOperations: number;
  slowestOperation: PerformanceMetrics | null;
  fastestOperation: PerformanceMetrics | null;
}

/**
 * Хук для мониторинга производительности операций выбора в таблице
 * Помогает выявить узкие места и оптимизировать производительность
 */
export const useSelectionPerformanceMonitor = (enabled: boolean = false) => {
  const metricsRef = useRef<PerformanceMetrics[]>([]);
  const [stats, setStats] = useState<PerformanceStats>({
    averageSelectTime: 0,
    averageSelectAllTime: 0,
    averageClearTime: 0,
    totalOperations: 0,
    slowestOperation: null,
    fastestOperation: null,
  });

  // Функция для записи метрики
  const recordMetric = useCallback((metric: PerformanceMetrics) => {
    if (!enabled) return;
    
    metricsRef.current.push(metric);
    
    // Ограничиваем количество сохраняемых метрик
    if (metricsRef.current.length > 1000) {
      metricsRef.current = metricsRef.current.slice(-500);
    }
  }, [enabled]);

  // Функция для измерения времени выполнения операции
  const measureOperation = useCallback(
    <T extends any[]>(
      operationType: PerformanceMetrics['operationType'],
      itemsCount: number,
      operation: (...args: T) => void
    ) => {
      return (...args: T) => {
        if (!enabled) {
          operation(...args);
          return;
        }

        const startTime = performance.now();
        operation(...args);
        const endTime = performance.now();
        
        recordMetric({
          operationType,
          duration: endTime - startTime,
          itemsCount,
          timestamp: Date.now(),
        });
      };
    },
    [enabled, recordMetric]
  );

  // Обновление статистики
  useEffect(() => {
    if (!enabled || metricsRef.current.length === 0) return;

    const metrics = metricsRef.current;
    const selectMetrics = metrics.filter(m => m.operationType === 'select');
    const selectAllMetrics = metrics.filter(m => m.operationType === 'selectAll');
    const clearMetrics = metrics.filter(m => m.operationType === 'clear');

    const calculateAverage = (arr: PerformanceMetrics[]) => 
      arr.length > 0 ? arr.reduce((sum, m) => sum + m.duration, 0) / arr.length : 0;

    const findSlowest = (arr: PerformanceMetrics[]) =>
      arr.reduce((slowest, current) => 
        !slowest || current.duration > slowest.duration ? current : slowest, 
        null as PerformanceMetrics | null
      );

    const findFastest = (arr: PerformanceMetrics[]) =>
      arr.reduce((fastest, current) => 
        !fastest || current.duration < fastest.duration ? current : fastest, 
        null as PerformanceMetrics | null
      );

    const allMetrics = [...selectMetrics, ...selectAllMetrics, ...clearMetrics];

    setStats({
      averageSelectTime: calculateAverage(selectMetrics),
      averageSelectAllTime: calculateAverage(selectAllMetrics),
      averageClearTime: calculateAverage(clearMetrics),
      totalOperations: metrics.length,
      slowestOperation: findSlowest(allMetrics),
      fastestOperation: findFastest(allMetrics),
    });
  }, [enabled, metricsRef.current.length]);

  // Функция для получения детальной статистики
  const getDetailedStats = useCallback(() => {
    if (!enabled) return null;

    const metrics = metricsRef.current;
    const now = Date.now();
    const oneMinuteAgo = now - 60000;
    const fiveMinutesAgo = now - 300000;

    const recentMetrics = metrics.filter(m => m.timestamp > oneMinuteAgo);
    const last5MinutesMetrics = metrics.filter(m => m.timestamp > fiveMinutesAgo);

    return {
      total: metrics.length,
      lastMinute: recentMetrics.length,
      last5Minutes: last5MinutesMetrics.length,
      averageDurationLastMinute: recentMetrics.length > 0 
        ? recentMetrics.reduce((sum, m) => sum + m.duration, 0) / recentMetrics.length 
        : 0,
      operationsByType: {
        select: metrics.filter(m => m.operationType === 'select').length,
        selectAll: metrics.filter(m => m.operationType === 'selectAll').length,
        clear: metrics.filter(m => m.operationType === 'clear').length,
      },
      performanceIssues: metrics.filter(m => m.duration > 100), // Операции дольше 100мс
    };
  }, [enabled]);

  // Функция для сброса метрик
  const resetMetrics = useCallback(() => {
    metricsRef.current = [];
    setStats({
      averageSelectTime: 0,
      averageSelectAllTime: 0,
      averageClearTime: 0,
      totalOperations: 0,
      slowestOperation: null,
      fastestOperation: null,
    });
  }, []);

  // Функция для логирования предупреждений о производительности
  const logPerformanceWarnings = useCallback(() => {
    if (!enabled) return;

    const detailedStats = getDetailedStats();
    if (!detailedStats) return;

    // Предупреждение о медленных операциях
    if (detailedStats.performanceIssues.length > 0) {
      console.warn(
        `[Selection Performance] Found ${detailedStats.performanceIssues.length} slow operations (>100ms):`,
        detailedStats.performanceIssues
      );
    }

    // Предупреждение о высокой частоте операций
    if (detailedStats.lastMinute > 50) {
      console.warn(
        `[Selection Performance] High operation frequency: ${detailedStats.lastMinute} operations in the last minute`
      );
    }

    // Предупреждение о средней производительности
    if (detailedStats.averageDurationLastMinute > 50) {
      console.warn(
        `[Selection Performance] Average operation time is high: ${detailedStats.averageDurationLastMinute.toFixed(2)}ms`
      );
    }
  }, [enabled, getDetailedStats]);

  // Автоматическое логирование предупреждений каждые 30 секунд
  useEffect(() => {
    if (!enabled) return;

    const interval = setInterval(logPerformanceWarnings, 30000);
    return () => clearInterval(interval);
  }, [enabled, logPerformanceWarnings]);

  return {
    measureOperation,
    stats,
    getDetailedStats,
    resetMetrics,
    enabled,
  };
};

/**
 * Хук для создания оптимизированных обработчиков с мониторингом производительности
 */
export const useMonitoredSelectionHandlers = (
  originalHandlers: {
    onSelect: (record: any, selected: boolean) => void;
    onSelectAll: (selected: boolean, selectedRows: any[], changeRows: any[]) => void;
    clearSelection: () => void;
  },
  itemsCount: number,
  monitoringEnabled: boolean = process.env.NODE_ENV === 'development'
) => {
  const { measureOperation } = useSelectionPerformanceMonitor(monitoringEnabled);

  const monitoredOnSelect = useCallback(
    measureOperation('select', 1, originalHandlers.onSelect),
    [measureOperation, originalHandlers.onSelect]
  );

  const monitoredOnSelectAll = useCallback(
    measureOperation('selectAll', itemsCount, originalHandlers.onSelectAll),
    [measureOperation, originalHandlers.onSelectAll, itemsCount]
  );

  const monitoredClearSelection = useCallback(
    measureOperation('clear', itemsCount, originalHandlers.clearSelection),
    [measureOperation, originalHandlers.clearSelection, itemsCount]
  );

  return {
    onSelect: monitoredOnSelect,
    onSelectAll: monitoredOnSelectAll,
    clearSelection: monitoredClearSelection,
  };
};

/**
 * Компонент для отображения статистики производительности (только для разработки)
 */
export const PerformanceStatsDisplay: React.FC<{ 
  stats: PerformanceStats;
  detailedStats: ReturnType<ReturnType<typeof useSelectionPerformanceMonitor>['getDetailedStats']>;
}> = ({ stats, detailedStats }) => {
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div style={{ 
      position: 'fixed', 
      top: 10, 
      right: 10, 
      background: 'rgba(0,0,0,0.8)', 
      color: 'white', 
      padding: '10px', 
      borderRadius: '4px',
      fontSize: '12px',
      zIndex: 9999,
      maxWidth: '300px'
    }}>
      <h4>Selection Performance Stats</h4>
      <div>Total Operations: {stats.totalOperations}</div>
      <div>Avg Select: {stats.averageSelectTime.toFixed(2)}ms</div>
      <div>Avg Select All: {stats.averageSelectAllTime.toFixed(2)}ms</div>
      <div>Avg Clear: {stats.averageClearTime.toFixed(2)}ms</div>
      {stats.slowestOperation && (
        <div>Slowest: {stats.slowestOperation.operationType} ({stats.slowestOperation.duration.toFixed(2)}ms)</div>
      )}
      {detailedStats && (
        <>
          <div>Last Minute: {detailedStats.lastMinute} ops</div>
          <div>Performance Issues: {detailedStats.performanceIssues.length}</div>
        </>
      )}
    </div>
  );
};
