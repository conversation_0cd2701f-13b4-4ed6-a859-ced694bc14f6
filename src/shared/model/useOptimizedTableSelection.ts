import { useMemo, useState, useCallback } from 'react';
import { deleteKeysFromObject } from './handleKeysFromObject';

/**
 * Оптимизированное состояние выбора в таблице с использованием Set для ключей и
 * Map для быстрого доступа к строкам
 */
export interface OptimizedTableSelectionState {
  // Внутренние оптимизированные структуры данных
  _keysSet: Set<Key>;
  _rowsMap: Map<Key, TableColumnsAndRows['rows'][0]>;
  keys: Key[];
  rows: TableColumnsAndRows['rows'];
}

type OptimizedTableSelectionStateCallback = (
  state: OptimizedTableSelectionState,
) => OptimizedTableSelectionState;

const createDefaultState = (): OptimizedTableSelectionState => ({
  keys: [],
  rows: [],
  _keysSet: new Set(),
  _rowsMap: new Map(),
});

/**
 * Оптимизированный хук для управления выбором строк таблицы Использует Set и
 * Map для O(1) операций поиска и модификации
 */
export const useOptimizedTableSelection = (
  defaultSelected?: Partial<OptimizedTableSelectionState>,
): readonly [OptimizedTableSelectionState, typeof handlers] => {
  const [selected, setSelected] = useState<OptimizedTableSelectionState>(() => {
    const defaultState = createDefaultState();
    if (defaultSelected?.keys) {
      defaultState.keys = [...defaultSelected.keys];
      defaultState._keysSet = new Set(defaultSelected.keys);
    }
    if (defaultSelected?.rows) {
      defaultState.rows = [...defaultSelected.rows];
      defaultState._rowsMap = new Map(
        defaultSelected.rows.map((row) => [row.key, row]),
      );
    }
    return defaultState;
  });

  const handlers = useMemo(
    () => ({
      clearSelection: () => setSelected(createDefaultState()),

      handleSelection: (
        state:
          | OptimizedTableSelectionState
          | OptimizedTableSelectionStateCallback,
      ) => {
        if (typeof state === 'function') {
          setSelected((prev) => {
            const newState = state(prev);
            // Синхронизируем внутренние структуры данных
            return {
              ...newState,
              _keysSet: new Set(newState.keys),
              _rowsMap: new Map(newState.rows.map((row) => [row.key, row])),
            };
          });
        } else {
          setSelected({
            ...state,
            _keysSet: new Set(state.keys),
            _rowsMap: new Map(state.rows.map((row) => [row.key, row])),
          });
        }
      },

      // Оптимизированная версия selectAll с батчированием
      selectAll: (allRows: import('features/DataGrid').TableRowData[]) => {
        const allFlatRows: import('features/DataGrid').TableRowData[] = [];
        const keysSet = new Set<Key>();
        const rowsMap = new Map<
          Key,
          import('features/DataGrid').TableRowData
        >();

        // Батчированная обработка всех строк
        allRows.forEach((row) => {
          if (row.children && row.children.length > 0) {
            // Добавляем детей
            row.children.forEach((child) => {
              allFlatRows.push(child);
              keysSet.add(child.key);
              rowsMap.set(child.key, child);
            });

            // Добавляем родителя без детей
            const parentWithoutChildren = deleteKeysFromObject(row, [
              'children',
            ]) as import('features/DataGrid').TableRowData;
            allFlatRows.push(parentWithoutChildren);
            keysSet.add(parentWithoutChildren.key);
            rowsMap.set(parentWithoutChildren.key, parentWithoutChildren);
          } else {
            allFlatRows.push(row);
            keysSet.add(row.key);
            rowsMap.set(row.key, row);
          }
        });

        setSelected({
          keys: Array.from(keysSet),
          rows: allFlatRows,
          _keysSet: keysSet,
          _rowsMap: rowsMap,
        });
      },

      // Быстрая проверка выбранности строки
      isSelected: (key: Key): boolean => selected._keysSet.has(key),

      // Быстрое получение выбранной строки
      getSelectedRow: (key: Key) => selected._rowsMap.get(key),

      // Батчированное добавление/удаление строк
      batchUpdate: (
        keysToAdd: Key[] = [],
        rowsToAdd: import('features/DataGrid').TableRowData[] = [],
        keysToRemove: Key[] = [],
      ) => {
        setSelected((prev) => {
          const newKeysSet = new Set(prev._keysSet);
          const newRowsMap = new Map(prev._rowsMap);

          // Добавляем новые элементы
          keysToAdd.forEach((key) => newKeysSet.add(key));
          rowsToAdd.forEach((row) => {
            newKeysSet.add(row.key);
            newRowsMap.set(row.key, row);
          });

          // Удаляем элементы
          keysToRemove.forEach((key) => {
            newKeysSet.delete(key);
            newRowsMap.delete(key);
          });

          return {
            keys: Array.from(newKeysSet),
            rows: Array.from(newRowsMap.values()),
            _keysSet: newKeysSet,
            _rowsMap: newRowsMap,
          };
        });
      },
    }),
    [selected._keysSet, selected._rowsMap],
  );

  return [selected, handlers];
};

/**
 * Хук для создания оптимизированных обработчиков выбора строк с поддержкой
 * иерархических данных
 */
export const useOptimizedSelectionHandlers = (
  handleSelection: ReturnType<
    typeof useOptimizedTableSelection
  >[1]['handleSelection'],
  rowChildrenMap: Map<Key, import('features/DataGrid').TableRowData[]>,
  flatRowsMap: Map<Key, import('features/DataGrid').TableRowData>,
) => {
  const onSelect = useCallback(
    (record: import('features/DataGrid').TableRowData, isSelected: boolean) => {
      const { key } = record;

      handleSelection((prevState) => {
        const keysSet = new Set(prevState.keys);
        const rowsMap = new Map(prevState.rows.map((row) => [row.key, row]));

        // Собираем все ключи для обработки
        const keysToProcess = [key];
        const children = rowChildrenMap.get(key);
        if (children) {
          children.forEach((child) => keysToProcess.push(child.key));
        }

        if (isSelected) {
          keysToProcess.forEach((processKey) => {
            keysSet.add(processKey);
            const rowData =
              processKey === key ? record : flatRowsMap.get(processKey);
            if (rowData) {
              rowsMap.set(processKey, rowData);
            }
          });
        } else {
          keysToProcess.forEach((processKey) => {
            keysSet.delete(processKey);
            rowsMap.delete(processKey);
          });
        }

        return {
          keys: Array.from(keysSet),
          rows: Array.from(rowsMap.values()),
          _keysSet: keysSet,
          _rowsMap: rowsMap,
        };
      });
    },
    [handleSelection, rowChildrenMap, flatRowsMap],
  );

  const onSelectAll = useCallback(
    (
      isSelected: boolean,
      _,
      changedRows: import('features/DataGrid').TableRowData[],
    ) => {
      handleSelection((prevState) => {
        const changedKeysSet = new Set<Key>();
        const changedRowsMap = new Map<
          Key,
          import('features/DataGrid').TableRowData
        >();

        changedRows.forEach((row) => {
          changedKeysSet.add(row.key);
          changedRowsMap.set(row.key, row);

          const children = rowChildrenMap.get(row.key);
          if (children) {
            children.forEach((child) => {
              changedKeysSet.add(child.key);
              changedRowsMap.set(child.key, child);
            });
          }
        });

        if (isSelected) {
          const existingKeysSet = new Set(prevState.keys);
          const existingRowsMap = new Map(
            prevState.rows.map((row) => [row.key, row]),
          );

          changedRowsMap.forEach((row, key) => {
            existingKeysSet.add(key);
            existingRowsMap.set(key, row);
          });

          return {
            keys: Array.from(existingKeysSet),
            rows: Array.from(existingRowsMap.values()),
            _keysSet: existingKeysSet,
            _rowsMap: existingRowsMap,
          };
        }

        return {
          keys: prevState.keys.filter((key) => !changedKeysSet.has(key)),
          rows: prevState.rows.filter((row) => !changedKeysSet.has(row.key)),
          _keysSet: new Set(
            prevState.keys.filter((key) => !changedKeysSet.has(key)),
          ),
          _rowsMap: new Map(
            prevState.rows
              .filter((row) => !changedKeysSet.has(row.key))
              .map((row) => [row.key, row]),
          ),
        };
      });
    },
    [handleSelection, rowChildrenMap],
  );

  return { onSelect, onSelectAll };
};
