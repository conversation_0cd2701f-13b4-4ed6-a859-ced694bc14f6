export const apiUrls = {
  presetConfigs: {
    getAll: 'get_all_presets',
    get: 'get_exact_presets',
    createUpdate: 'create_update_presets',
    delete: 'delete_presets',
  },
  directoryOfOrganizations: {
    tabs: 'directoryOfOrganization',
  },
  auth: '/authenticated',
  sideBar: '/sidebar',
  defaultPages: {
    details: (endpoint: Endpoint): string => `${endpoint}_details`,
    filters: (endpoint: Endpoint): string => `${endpoint}_filters`,
    group: (endpoint: Endpoint): string => `${endpoint}_group`,
    sorting: (endpoint: Endpoint): string => `${endpoint}_sorting`,
    treeFolders: (endpoint: Endpoint): string => `${endpoint}_tree/folders`,
    treeFiles: (endpoint: Endpoint): string => `${endpoint}_tree/files`,
    lazyGroup: (endpoint: Endpoint): string => `${endpoint}_detailed_group`,
  },
  download: {
    report: (endpoint: Endpoint): string => `${endpoint}_report`,
    excelExport: (endpoint: Endpoint): string => `${endpoint}_export_to_excel`,
  },
  blitz: {
    logout: '/logout',
    config: '/configuration',
  },
  reportPermissions: 'report_permissions',
  permission: {
    excel: {
      export: 'control_role_xls_export',
      import: 'control_role_xls_import',
    },
    reports: {
      groupsSelector: 'selector_report_group',
      documentType: 'document_type',
      reportTable: 'control_report_table',
      fileId: 'control_report_file_id',
      fileState: 'control_report_file_state',
      fileDownload: 'control_report_file_download',
    },
    tabs: {
      users: 'control_users',
      groups: 'control_group',
      roles: 'control_role',
      reports: 'control_report',
    },
    users: {
      table: 'control_users',
      userById: (id: string) => `control_users/${id}`,
      groups: 'control_user_groups',
      roles: 'control_user_roles',
      rights: 'control_user_effective_permission',
      activate: 'control_activate_user',
      deActivate: 'control_deactivate_user',
      profile: 'control_user_profile',
      defaultProfile: 'control_default_profile',
      link: {
        groups: 'link_user_groups',
        roles: 'link_user_roles',
      },
    },
    groups: {
      table: 'control_group',
      groupById: (id: string) => `control_group/${id}`,
      users: 'control_group_users',
      roles: 'control_group_roles',
      rights: 'control_group_effective_permission',
      restore: 'restore_group',
      delete: 'delete_group',
      createUpdate: 'create_update_group',
      link: {
        roles: 'link_group_roles',
        users: 'link_group_users',
      },
    },
    roles: {
      table: 'control_role',
      roleById: (id: string) => `control_role/${id}`,
      permission: 'control_all_role_permission',
      plPermissionByType: 'control_all_role_questions_pl',
      effectivePermission: 'control_all_role_effective_permission',
      group: 'control_role_group',
      users: 'control_role_users',
      restore: 'restore_role',
      delete: 'delete_role',
      linkPermission: 'link_role_permissions',
      update: 'update_role',
      create: 'create_role',
      copy: 'copy_role',
      link: {
        permissions: 'link_role_permissions',
        groups: 'link_role_groups',
        users: 'link_role_users',
      },
    },
    globalLink: 'mass_link',
    dateRange: {
      get: 'get_all_date_range_type',
      save: 'control_save_date_range',
    },
    matrix: 'control_document_permission_matrix',
  },
  fileNet: {
    filenetIndexingInfo: '/main/indexing/info',
    filenetCardInfo: (popupId: string | number): string =>
      `publication/get/${popupId}`,
    dossierRubrics: 'publication/audit/associations/rubrics/get',
    attachedFiles: 'publication/audit/associations/doc/get',
    assDocsFiles: 'publication/audit/associations/assdocs/get',
    reportsChangelog: '/report/index',
    rubrics: 'rubric/find/as/tree',
    viewFile: (attachmentId: string | number): string =>
      `renderer/link/${attachmentId}`,
    sendToGluster: (attachmentId: string | number) =>
      `/publication/file/do/download/attachmentid/${attachmentId}`,
    downloadFile: (attachmentId: string | number): string =>
      `publication/do/download/stream/${attachmentId}`,
    fileCard: (popupId: string | number) =>
      `file-net?tableType=cards&popupId=${popupId}`,
    dossierCard: (popupId: string | number) =>
      `file-net?tableType=dossier&popupId=${popupId}`,
    report: {
      table: 'report/find/as/datatable',
      materialIdentification: (id: string | number) =>
        `report/document/${id}/title`,
      postWorkForm: 'report/do/build/USERS_JOB_WITH_MATERIALS',
      postUserForm: 'report/do/build/USERS_USING_SYSTEM',
    },
    dossierAndCardsTable: 'main/find/documents',
    filters: {
      fromFn: 'ou/find/as/treetable',
      gibr: 'get_file_net_table_gibr',
      ko: 'get_file_net_table_ko',
      nfo: 'get_file_net_table_nfo',
      territorial: 'get_file_net_table_area',
    },
    fileStructure: 'get_file_net_table_structure',
    dossierStructure: {
      tree: (popupId: string | number) =>
        `publication/audit/${popupId}/tree/top?addFiles=true`,
      file: 'publication/info/by/path',
      subTree: (popupId: string | number) =>
        `publication/audit/${popupId}/tree/subtree`,
    },
    download: '/control/v2/download/report',
    attachmentsTree: '/publication/v2/get/as/tree',
    attachmentsTreeStream: '/publication/v2/get/as/tree/stream',
  },
  fileNetSync: {
    findRepos: 'control/find/replicator/repositories',
    monitoring: 'monitoring/summary',
    uploadLog: 'upload_log',
    replicatorRepos: 'file_net_sync_replicator_repositories',
    configScheduler:
      'control/find/replicator/repositories/setRequestPackageRepositoriesList',
    startScheduler: 'control/command/startScheduler',
    stopScheduler: 'control/command/stopScheduler',
    immediatelyUpdateScheduler: 'control/command/requestPackagesImmediately',
    schedulerSettings: 'control/command/changeSchedulerSettings',
  },
  replicator: {
    report: 'replicator',
    progress: 'replicator_progress',
    getLastFileDate: 'workgroup-cabinet/import/departments/log/latest',
    postCatalog: 'zuul/workgroup-cabinet/import/departments',
    data: 'replicator',
    log: {
      getDetails: 'replicator_report_details',
    },
    settings: {
      interruptReplication: 'replicator_interrupt_uploading',
      startReplication: 'replicator_start_uploading',
      restoreReplication: 'replicator_restore_uploading',
      setSettings: 'replicator_set_params',
      getSettings: 'replicator_get_params',
    },
  },
  userNotice: {
    notice: {
      table: 'notifications',
      readAllNotice: 'notifications/-/read',
      readSingleNotice: (noticeId: string) => `notifications/${noticeId}/read`,
      mailSubscribe: 'settings/receive-email',
    },
    subscriptions: {
      allSubscriptions: 'subscriptions',
      unsubscribe: (id: string) => `subscriptions/${id}`,
      controlLists: 'control-lists',
      controlListsSubscribed: 'control-lists/subscribed',
      fileList: 'rubrics',
    },
  },
  workGroup: {
    mainTable: 'krg3',
    details: 'krg3_details',
    members: 'krg_members',
    mainTabs: 'krg3_main_tabs',
    info: 'krg3_info',
    permissions: 'krg3_permissions',
    wildcardPermissions: 'krg3_epc_wildcards',
    fileSearch: {
      postSearch: 'krg3_file_search',
    },
    nestedTabsEndpoints: {
      request: 'krg3_request_notice',
      removeRequestOrInternalFileLink: 'remove_request_file_link',
      packageDef: 'krg3_input_package_def',
      internal: 'krg3_internal_notices',
      fileData: 'krg3_file_data',
    },
    tabsEndpoints: {
      inputOutputDef: 'krg3_input_output_def_tabs',
      members: 'krg_members',
      defInventory: 'krg3_inventory',
      epc: 'krg3_epp_epp_description',
      noticeFeed: 'krg3_notification_feed',
    },
    notification: {
      checkUnread: 'krg3_notification_feed_unread_messages',
      notificationFeed: 'krg3_notification_feed',
      markAsRead: 'krg3_mark_as_read',
    },
    bindFiles: {
      defBind: {
        getTable: 'krg3_input_package_def_file_link',
        save: 'save_def_file_link',
      },
      requestBind: {
        getTable: 'krg3_request_file_link',
        save: 'save_request_file_link',
      },
      internal: {
        getTable: 'krg3_internal_notices',
        save: 'save_internal_file_link',
      },
    },
    reports: {
      getCabinetReportFileId: 'krg3_wgc_report_file_id',
      getCabinetReportFileState: 'krg3_wgc_report_file_state',
      getCabinetReportFileDownload: 'krg3_wgc_report_file_download',
      getInventoryTableReport: 'krg3_inventory_export_to_excel',
      getEpcTableReport: 'krg3_epp_epp_description_export_to_excel',
    },
    inventory: {
      updateComment: 'krg3_update_inventory_comment',
    },
    request: {
      create: 'krg3_request_notice_create',
      update: 'krg3_request_notice_update',
      startUpdate: 'krg3_request_notice_start_update',
      canUpdateCheck: 'krg3_request_notice_try_lock',
      sendSadd: 'krg3_request_notice_send_sadd',
      deleteRequest: 'krg3_request_notice_delete',
      markMainFile: 'krg3_mark_request_notice_file_main',
      switchCollapseItem: 'krg3_request_item/status/switch_completed',
      switchRequestStatus: 'krg3_request_notice/status/switch_completed',
      upload: 'krg3_upload/request_notice',
      outputFiles: 'krg3_request_output_files',
      getFileTree: 'krg3_uploading_epc_tree',
      getAdditionalData: 'krg3_request_notice_popup',
      saveDiffFiles: 'krg3_request_notice/file_diff',
      downloadFiles: 'krg3_download_manager/link/download',
      downloaderStatus: (id: string): string =>
        `krg3_download_manager/link/upload/session/${id}`,
      getMainData: 'krg3_request_notice_popup',
      getAccessSelect: 'krg3_access_stamps',
      getQuestions: 'krg3_request_notice_questions',
      switchActual: 'krg3_request_notice/status/switch_active',
      cancelEdit: 'krg3_request_notice_edit_cancel',
      renewTimer: 'krg3_request_notice_edit_prolongate',
      getEmployeeData: 'krg3_request_notice_employees',
    },
    internal: {
      create: 'krg3_internal_notice_create',
      update: 'krg3_internal_notice_update',
      sendSadd: 'krg3_internal_notice_send_sadd',
      deleteInternal: 'krg3_internal_delete',
      markMainFile: 'krg3_mark_internal_notice_file_main',
      switchInternalStatus: 'krg3_internal_notice/status/switch_completed',
      upload: 'krg3_upload/internal_notice',
      saveDiffFiles: 'krg3_internal_notice/file_diff',
      getAdditionalData: 'krg3_internal_notice',
      getMainData: 'krg3_internal_notice',
      switchActual: 'krg3_internal_notice/status/switch_active',
    },
    fileData: {
      deleteFile: 'krg3_delete_files',
      deleteFileById: (fileId: string) => `/krg3_file/${fileId}`,
      downloadFileById: (fileId: string) => `krg3_download_file/${fileId}`,
      downloadFileByIdWithSign: (fileId: string) =>
        `krg3_download_file/${fileId}/sign`,
      fileViewer: (fileId: string) => `krg3_file/${fileId}/renderer`,
      fileNetViewer: (fileId: string) => `krg3_file/filenet/${fileId}/renderer`,
      fileVisibility: 'krg3_file_data/visibility',
    },
    upload: {
      uploadBlob: 'krg3_upload/omni',
      getTree: 'krg3_uploading_epc_tree',
      uploadFileWithUploader: (
        cabinetId: string,
        itemId: string,
        source: number,
      ) =>
        `krg3_download_manager/link/upload/omni/${cabinetId}/${itemId}/${source}`,
    },
    inspection: {
      instance: 'krg3_sadd_instance',
      persons: 'krg3_sadd_person',
    },
    passport: {
      getCurrentTree: 'krg3_current_epc_tree',
      allCatalogs: 'krg3_system_directory_tree',
      moveTreeNode: 'krg3_current_epc_tree_move',
      deleteTreeNode: 'krg3_current_epc_tree_delete',
      createTreeNode: 'krg3_current_epc_tree_create',
      updateTreeNode: 'krg3_current_epc_tree_update',
      search: 'krg3_tree_search',
      saveMark: 'krg3_tree_mark',
    },
    packageDef: {
      unlinkDefFile: 'krg3_remove_def_file_link',
    },
    EPC: {
      search: 'krg3_epc_tree_search',
      getTree: 'krg3_epc_tree',
      getLazyTree: '/krg/epc-tree',
      getLazyTreePage: '/krg/epc-tree-page',
      searchLazyTree: '/krg/epc-tree-search',
      getLinkedElements: (itemId: TreeElement['itemId']) =>
        `workgroup-cabinet/directory/${itemId}/linked`,
      saveLinkedElements: 'krg3_directory/linked_elem',
      updateRecord: 'krg3_epp_update',
      saveMove: 'krg3_epc_move',
      downloadEPC: (cabinetId: string) =>
        `/workgroup-cabinet/${cabinetId}/epc/download`,
      markMainFile: (fileId: string) => `krg3_epc_main/${fileId}`,
      getRouteFilter: 'krg3_file_data/routes',
      getStatusDefFilter: 'krg3_input_package_def/statuses',
      copyFiles: 'krg3_epc_copy_files',
      copyFilesWithCatalogs: 'krg3_epc_copy_files_with_catalogs',
    },
  },
  workGroupControl: {
    path: 'work-group-control',
    mainTable: 'krg4',
    details: 'krg4_details',
    mainTabs: 'krg4_main_tabs',
    filters: 'krg4_filters',
    getCount: 'krg4_count',
    settings: 'krg4_settings',
    catalogs: {
      getTree: 'krg4_tree',
      moveTreeNode: 'krg4_tree_move',
      markTreeNode: 'krg4_tree_mark',
      getMark: 'krg_tree_mark',
      deleteTreeNode: 'krg4_tree_delete',
      search: 'krg4_tree_search',
      history: 'krg4_tree_history',
      postSave: 'krg4_tree_save',
      addTreeNode: 'krg4_tree_create',
      updateTreeNode: 'krg4_tree_update',
    },
    creationPlan: {
      getTable: 'krg4_creation_plan',
      createCabinet: 'krg4_creation_plan_create',
      updateTale: 'krg4_creation_plan_update',
      getReport: 'krg4_creation_plan_xlsx',
    },
    composition: {
      updateUser: 'krg4_update_user',
      createUser: 'krg4_create_user',
      unblockUser: 'krg4_unblock_user',
      blockUser: 'krg4_block_user',
      deleteUser: 'krg4_delete_user',
      getSelect: 'krg4_select_permissions',
    },
    notification: {
      createNotification: 'krg4_create_notification',
      updateNotification: 'krg4_update_notification',
      sendNotification: 'krg4_send_notification',
      deleteNotification: 'krg4_delete_notification',
      getSelect: 'krg4_create_notification_select',
      checkStatus: 'krg4_check_notification',
      resendNotification: (noticeId: string) =>
        `krg4_notification/${noticeId}/resend`,
    },
    reports: {
      fileId: 'krg4_wgc_report_file_id',
      fileState: 'krg4_wgc_report_file_state',
      fileDownload: 'krg4_wgc_report_file_download',
    },
  },
  workGroupControlNotice: {
    table: 'notification_list',
    filters: 'notification_list_filters',
  },
};
