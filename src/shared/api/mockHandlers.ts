/* eslint-disable */
import { http, HttpResponse } from 'msw';
import { delay } from 'shared/lib/delay';

// Mock data imports
import {
  packageDefTableData,
  packageFilesData,
  packageFilesColumns,
  requestItemsData,
  requestItemsColumns,
} from './mockData';
import {
  eppTreeMockData,
  krgTreeRootMockData,
} from './mockData/eppTreeMockData';
import { krg3RequestNoticeMockData } from './mockData/krg3RequestNoticeMockData';
import { krgTableMockData } from './mockData/krgTableMockData';
import { krg3NoticeForRequestMockData } from './mockData/notices';
import krg3InternalNoticesMockData from './mockData/krg3_internal_notices';
import {
  assDocs,
  filenetMainFindDocuments,
  filenetPublicationGet,
  filenetRubrics,
  rubricTree,
} from './mockData/filenet-main-find-documents';
import { krg4CreationPlan } from './mockData/u-krg-table-mock-data';

// Constants
const DEFAULT_DELAY = 500;

// ============================================================================
// DYNAMIC MOCK STATE - For simulating data changes on refetch
// ============================================================================

/**
 * Состояние для симуляции изменений данных при refetch
 * Каждый раз при запросе к krg3_internal_notices данные будут немного изменяться
 */
let internalNoticesState = {
  requestCount: 0,
  lastMainFileId: null as string | null,
};

// ============================================================================
// INTERACTIVE MOCK DATA GENERATOR - Random Directory Catalogs
// ============================================================================

/**
 * Хранилище информации о каталогах
 */
const catalogFilesRegistry = new Map<string, number>();

/**
 * 🎲 ИНТЕРАКТИВНЫЙ ГЕНЕРАТОР СЛУЧАЙНЫХ КАТАЛОГОВ
 *
 * Этот генератор автоматически создает случайные каталоги директорий для любых
 * неизвестных itemId в запросах к /kzid_rest/krg/epc-tree
 *
 * 📋 ПАРАМЕТРЫ ЗАПРОСА:
 *
 * - ItemsCount: количество элементов (по умолчанию 10)
 * - Scenario: сценарий генерации • 'mixed' - смешанные файлы и директории (по
 *   умолчанию) • 'files-only' - только файлы • 'dirs-only' - только директории
 *   • 'deep-nested' - глубокая вложенность (80% директорий)
 *
 * 🔧 ПРИМЕРЫ ИСПОЛЬЗОВАНИЯ:
 *
 * 1. Базовый запрос (10 смешанных элементов): GET
 *    /kzid_rest/krg/epc-tree?itemId=новый-uuid
 * 2. Только файлы (20 штук): GET
 *    /kzid_rest/krg/epc-tree?itemId=новый-uuid&scenario=files-only&itemsCount=20
 * 3. Только директории (5 штук): GET
 *    /kzid_rest/krg/epc-tree?itemId=новый-uuid&scenario=dirs-only&itemsCount=5
 * 4. Глубокая структура (15 элементов): GET
 *    /kzid_rest/krg/epc-tree?itemId=новый-uuid&scenario=deep-nested&itemsCount=15
 *
 * ✨ ОСОБЕННОСТИ:
 *
 * - Все itemId генерируются как уникальные UUID v4
 * - Названия каталогов и файлов реалистичные (на русском языке)
 * - Поддерживаются маркеры каталогов [t], [i], [m], [x]
 * - Случайные значения для всех полей (permissions, isFixed, disabled и т.д.)
 * - Правильная структура ключей и позиций
 * - Различные расширения файлов (.xlsx, .docx, .pdf и т.д.)
 * - Различные расширения файлов (.xlsx, .docx, .pdf и т.д.)
 *
 * 📁 ОБРАБОТКА ПУСТЫХ КАТАЛОГОВ:
 *
 * - IsLeaf = true → каталог полностью пустой (нет содержимого)
 * - IsLeaf = false → каталог может содержать подкаталоги
 * - При запросе содержимого листового каталога возвращается пустой результат
 * - При запросе содержимого пустого не-листового каталога генерируются пустые
 *   подкаталоги
 */

/** Генератор уникальных UUID v4 */
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/** Генератор случайных названий каталогов */
function generateRandomDirectoryName(): string {
  const prefixes = [
    'Документы',
    'Материалы',
    'Отчеты',
    'Проекты',
    'Архив',
    'Реестры',
    'Уведомления',
    'Заявки',
    'Пакеты',
    'Результаты',
    'Ведомости',
    'Справки',
    'Протоколы',
    'Планы',
    'Контроль',
  ];

  const suffixes = [
    '2024',
    '2025',
    'Новые',
    'Обработанные',
    'Входящие',
    'Исходящие',
    'Временные',
    'Постоянные',
    'Рабочие',
    'Финальные',
  ];

  const markers = ['', ' [t]', ' [i]', ' [m]', ' [x]'];

  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const suffix =
    Math.random() > 0.6
      ? ` ${suffixes[Math.floor(Math.random() * suffixes.length)]}`
      : '';
  const marker = markers[Math.floor(Math.random() * markers.length)];

  return `${prefix}${suffix}${marker}`;
}

/** Генератор случайных названий файлов */
function generateRandomFileName(): string {
  const names = [
    'отчет',
    'документ',
    'справка',
    'протокол',
    'ведомость',
    'реестр',
    'план',
    'заявка',
    'уведомление',
    'файл',
  ];

  const extensions = ['.xlsx', '.docx', '.pdf', '.doc', '.xls'];
  const dates = ['01.01.2024', '15.03.2024', '30.06.2024', '12.12.2024'];

  const name = names[Math.floor(Math.random() * names.length)];
  const date =
    Math.random() > 0.5
      ? `_${dates[Math.floor(Math.random() * dates.length)]}`
      : '';
  const number =
    Math.random() > 0.7 ? `_${Math.floor(Math.random() * 1000) + 1}` : '';
  const ext = extensions[Math.floor(Math.random() * extensions.length)];

  return `${name}${date}${number}${ext}`;
}

/** Генератор случайного цвета для элементов дерева */
function generateRandomColor(isDirectory: boolean): string {
  if (isDirectory) {
    return 'rgba(0, 0, 0, 0.65)'; // Стандартный цвет для директорий
  } else {
    // Цвета для файлов
    const colors = [
      'rgba(0,0,255,0.5)', // Синий
      'rgba(255,0,0,0.5)', // Красный
      'rgba(0,128,0,0.5)', // Зеленый
      'rgba(255,165,0,0.5)', // Оранжевый
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }
}

/**
 * Расширенный генератор данных дерева каталогов
 *
 * @param scenario - Сценарий генерации ('mixed', 'files-only', 'dirs-only',
 *   'deep-nested')
 * @param parentId - ID родительского элемента
 * @param itemsCount - Количество элементов
 * @param expectedTotalFiles - Ожидаемое общее количество файлов
 * @returns Сгенерированные данные дерева каталогов
 */
function generateCatalogByScenario(
  scenario: 'mixed' | 'files-only' | 'dirs-only' | 'deep-nested' = 'mixed',
  parentId: string = generateUUID(),
  itemsCount: number = 10,
  expectedTotalFiles?: number,
): { treeData: TreeElement[]; history: any[]; foundNodes: any[]; debug?: any } {
  const treeData: TreeElement[] = [];

  // Если указано ожидаемое количество файлов, используем его, иначе генерируем случайно
  const totalFilesToDistribute =
    expectedTotalFiles !== undefined
      ? expectedTotalFiles
      : Math.floor(Math.random() * itemsCount * 3) + itemsCount;
  let remainingFiles = totalFilesToDistribute;

  // Если есть ожидаемое количество файлов, корректируем itemsCount для соответствия
  let actualItemsCount = itemsCount;
  if (expectedTotalFiles !== undefined) {
    if (expectedTotalFiles === 0) {
      // Специальный случай: каталог должен содержать 0 файлов
      // Но может содержать пустые подкаталоги (не листовые)
      actualItemsCount = Math.floor(Math.random() * 3) + 1; // 1-3 пустых подкаталога
      scenario = 'dirs-only'; // Только каталоги, без файлов
    } else {
      // Для корректного распределения нужно учесть сценарий
      switch (scenario) {
        case 'files-only':
          // Все элементы - файлы, поэтому itemsCount = expectedTotalFiles
          actualItemsCount = expectedTotalFiles;
          break;
        case 'dirs-only':
          // Все элементы - директории, файлы будут внутри них
          actualItemsCount = Math.min(
            itemsCount,
            Math.max(1, Math.ceil(expectedTotalFiles / 10)),
          );
          break;
        case 'mixed':
        case 'deep-nested':
        default:
          // Смешанный режим: используем исходное количество элементов
          // Файлы будут распределены между элементами для достижения expectedTotalFiles
          actualItemsCount = itemsCount;
          break;
      }
    }
  }

  // Рассчитываем оптимальное количество директорий и файлов
  let directoryCount = 0;
  let directFilesCount = 0;

  if (expectedTotalFiles !== undefined) {
    // Точный расчет для достижения expectedTotalFiles
    switch (scenario) {
      case 'files-only':
        directoryCount = 0;
        directFilesCount = actualItemsCount; // Все элементы - файлы
        break;
      case 'dirs-only':
        directoryCount = actualItemsCount; // Все элементы - директории
        directFilesCount = 0;
        break;
      case 'mixed':
      case 'deep-nested':
      default:
        if (expectedTotalFiles === 0) {
          // Пустой каталог: только директории, без файлов
          directoryCount = actualItemsCount;
          directFilesCount = 0;
        } else {
          // Смешанный режим: сбалансированное распределение
          if (expectedTotalFiles <= actualItemsCount) {
            // Если файлов мало, делаем больше прямых файлов
            directFilesCount = Math.max(
              1,
              Math.floor(expectedTotalFiles * 0.6),
            ); // 60% файлов - прямые
            directoryCount = Math.min(
              actualItemsCount - directFilesCount,
              Math.ceil((expectedTotalFiles * 0.4) / 3),
            ); // Остальные - директории, но не более чем нужно
          } else {
            // Если файлов много, делаем больше директорий
            directFilesCount = Math.min(
              actualItemsCount,
              Math.max(1, Math.floor(actualItemsCount * 0.3)),
            ); // 30% элементов - прямые файлы
            directoryCount = actualItemsCount - directFilesCount;
          }

          // Проверяем, что у нас есть хотя бы одна директория, если нужно распределить файлы
          const filesForDirectories = expectedTotalFiles - directFilesCount;
          if (filesForDirectories > 0 && directoryCount === 0) {
            // Нужна хотя бы одна директория
            directoryCount = 1;
            directFilesCount = actualItemsCount - 1;
          }
        }
        break;
    }
  } else {
    // Случайное распределение для старой логики
    for (let i = 0; i < actualItemsCount; i++) {
      let isDirectory: boolean;

      switch (scenario) {
        case 'files-only':
          isDirectory = false;
          break;
        case 'dirs-only':
          isDirectory = true;
          break;
        case 'deep-nested':
          isDirectory = i < Math.ceil(actualItemsCount * 0.8);
          break;
        case 'mixed':
        default:
          isDirectory = Math.random() > 0.4;
          break;
      }

      if (isDirectory) {
        directoryCount++;
      } else {
        directFilesCount++;
      }
    }
  }

  // directFilesCount уже рассчитан выше

  // Если есть директории, распределяем файлы между ними и прямыми файлами
  const filesPerDirectory: number[] = [];

  // Для expectedTotalFiles нужно точно распределить все файлы
  let filesForDirectories: number;
  let actualDirectFilesCount: number;

  if (expectedTotalFiles !== undefined) {
    // Точное распределение для соответствия expectedTotalFiles
    if (expectedTotalFiles === 0) {
      filesForDirectories = 0;
      actualDirectFilesCount = 0;
    } else if (directoryCount === 0) {
      // Только прямые файлы
      filesForDirectories = 0;
      actualDirectFilesCount = expectedTotalFiles;
    } else if (directFilesCount === 0) {
      // Только файлы в директориях
      filesForDirectories = expectedTotalFiles;
      actualDirectFilesCount = 0;
    } else {
      // Смешанный режим: распределяем между прямыми файлами и директориями
      actualDirectFilesCount = Math.min(
        directFilesCount,
        Math.floor(expectedTotalFiles * 0.3),
      ); // 30% прямых файлов
      filesForDirectories = expectedTotalFiles - actualDirectFilesCount;
    }
  } else {
    // Случайное распределение
    filesForDirectories = Math.max(0, remainingFiles - directFilesCount);
    actualDirectFilesCount = directFilesCount;
  }

  if (directoryCount > 0) {
    if (expectedTotalFiles === 0) {
      // Специальный случай: все директории должны быть пустыми (0 файлов)
      for (let i = 0; i < directoryCount; i++) {
        filesPerDirectory.push(0);
      }
    } else if (filesForDirectories > 0) {
      for (let i = 0; i < directoryCount; i++) {
        if (i === directoryCount - 1) {
          // Последней директории отдаем все оставшиеся файлы
          filesPerDirectory.push(filesForDirectories);
        } else {
          // Случайно распределяем файлы, но оставляем минимум для остальных директорий
          const maxForThisDir = Math.max(
            1,
            filesForDirectories - (directoryCount - i - 1),
          );
          const filesForThisDir = Math.floor(Math.random() * maxForThisDir) + 1;
          filesPerDirectory.push(filesForThisDir);
          filesForDirectories -= filesForThisDir;
        }
      }
    } else {
      // Если нет файлов для директорий, каждая получает минимум 1
      for (let i = 0; i < directoryCount; i++) {
        filesPerDirectory.push(1);
      }
    }
  }

  let directoryIndex = 0;
  let fileIndex = 0;

  // Определяем, сколько элементов нужно создать
  const totalElementsToCreate =
    expectedTotalFiles !== undefined
      ? directoryCount + directFilesCount
      : actualItemsCount;

  for (let i = 0; i < totalElementsToCreate; i++) {
    let isDirectory: boolean;

    // Строго следуем плану распределения
    if (expectedTotalFiles !== undefined) {
      // Точный контроль: создаем элементы согласно рассчитанному плану
      const remainingDirectories = directoryCount - directoryIndex;
      const remainingFiles = directFilesCount - fileIndex;
      const remainingItems = totalElementsToCreate - i;

      if (remainingDirectories === 0) {
        isDirectory = false; // Только файлы
      } else if (remainingFiles === 0) {
        isDirectory = true; // Только директории
      } else if (remainingItems === remainingDirectories) {
        isDirectory = true; // Нужно создать все оставшиеся директории
      } else if (remainingItems === remainingFiles) {
        isDirectory = false; // Нужно создать все оставшиеся файлы
      } else {
        // Случайный выбор среди допустимых вариантов
        isDirectory = Math.random() > 0.5;
      }
    } else {
      // Старая логика для случайной генерации
      switch (scenario) {
        case 'files-only':
          isDirectory = false;
          break;
        case 'dirs-only':
          isDirectory = true;
          break;
        case 'deep-nested':
          isDirectory = i < Math.ceil(actualItemsCount * 0.8);
          break;
        case 'mixed':
        default:
          isDirectory = Math.random() > 0.4;
          break;
      }
    }

    const itemId = generateUUID();
    const key = `0-0-${i}`;

    const baseItem = {
      itemId,
      parent: parentId,
      key,
      position: i,
      disabled: Math.random() > 0.8,
      checked: false,
      cabinetOnly: Math.random() > 0.9,
      tagged: false,
      marked:
        Math.random() > 0.95
          ? ['t', 'i', 'm'][Math.floor(Math.random() * 3)]
          : '',
      linked: [],
      children: [],
      isLeaf: !isDirectory,
      isDirectory,
      isCallable: false,
      color: generateRandomColor(isDirectory),
    };

    if (isDirectory) {
      const title = generateRandomDirectoryName();
      const filesCount = filesPerDirectory[directoryIndex] || 0;
      directoryIndex++;

      // Определяем, является ли каталог листовым (пустым)
      const isLeafDirectory = filesCount === 0 && Math.random() > 0.5;

      // Сохраняем информацию о каталоге в реестр
      catalogFilesRegistry.set(itemId, filesCount);

      treeData.push({
        ...baseItem,
        title,
        rawTitle: title.replace(/\s*\[[^\]]*\]$/, ''),
        isLeaf: isLeafDirectory,
        hasLinked: Math.random() > 0.85,
        isFixed: Math.floor(Math.random() * 4) as 0 | 1 | 2 | 3,
      });
    } else {
      const title = generateRandomFileName();

      treeData.push({
        ...baseItem,
        title,
        rawTitle: title,
        isMain: Math.floor(Math.random() * 2) as 0 | 1,
        fileNetId: itemId,
        fileSignExist: Math.random() > 0.8,
        permissions: {
          canView: Math.random() > 0.05,
          canDownload: Math.random() > 0.15,
        },
        isRemovable: Math.random() > 0.2,
      });
      fileIndex++;
    }
  }

  return {
    treeData,
    history: [],
    foundNodes: [],
    // Дополнительная отладочная информация
    debug: {
      expectedTotalFiles,
      plannedDirectFilesCount: directFilesCount,
      actualDirectFilesCount: fileIndex,
      filesForDirectories:
        expectedTotalFiles !== undefined
          ? expectedTotalFiles === 0
            ? 0
            : filesForDirectories
          : undefined,
      plannedDirectoryCount: directoryCount,
      actualDirectoryCount: directoryIndex,
    },
  };
}

// ============================================================================
// KRG3 HANDLERS - Main table endpoints and nested data
// ============================================================================

/** KRG3 main table handlers */
const krg3MainHandlers = [
  // Basic KRG3 table
  http.post('http://127.0.0.1:18080/kzid_rest/krg3', async ({ request }) => {
    const url = new URL(request.url);
    const page = url.searchParams.get('page') || '1';
    const size = url.searchParams.get('size') || '10';

    await delay(DEFAULT_DELAY);
    return HttpResponse.json(
      {
        ...krgTableMockData,
        page: parseInt(page, 10),
        size: parseInt(size, 10),
      },
      { status: 200 },
    );
  }),

  // Package definition table with lazy tabs
  http.post(
    'http://127.0.0.1:18080/kzid_rest/krg3_input_package_def',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = url.searchParams.get('pageNumber') || '1';
      const pageSize = url.searchParams.get('pageSize') || '10';

      await delay(DEFAULT_DELAY);
      return HttpResponse.json(
        {
          ...packageDefTableData,
          pagination: {
            total: 3,
            pageSize: parseInt(pageSize, 10),
          },
          pageNumber: parseInt(pageNumber, 10),
        },
        { status: 200 },
      );
    },
  ),

  // Request notice table with lazy tabs
  http.post(
    'http://127.0.0.1:18080/kzid_rest/krg3_request_notice',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = url.searchParams.get('pageNumber') || '1';
      const pageSize = url.searchParams.get('pageSize') || '10';

      await delay(DEFAULT_DELAY);
      return HttpResponse.json(
        krg3RequestNoticeMockData(pageNumber, pageSize),
        { status: 200 },
      );
    },
  ),

  // Internal notices table with preloaded nested tabs
  http.post(
    'http://127.0.0.1:18080/kzid_rest/krg3_internal_notices',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = url.searchParams.get('pageNumber') || '1';
      const pageSize = url.searchParams.get('pageSize') || '10';

      await delay(DEFAULT_DELAY);

      // Увеличиваем счетчик запросов
      internalNoticesState.requestCount++;

      // Создаем копию данных для возможности изменения
      const mockData = JSON.parse(JSON.stringify(krg3InternalNoticesMockData));

      // Симулируем изменения данных при каждом refetch
      if (mockData.rows && mockData.rows[0]?.nestedTable?.tabs) {
        const filesTab = mockData.rows[0].nestedTable.tabs.find((tab: any) => tab.key === '5');
        if (filesTab?.tableData?.rows) {
          // Обновляем дату последнего изменения
          const now = new Date();
          const formattedDate = now.toLocaleString('ru-RU', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          });

          mockData.rows[0].dateLastChange = formattedDate;

          // Каждый четный запрос - меняем основной файл
          if (internalNoticesState.requestCount % 2 === 0) {
            // Сбрасываем isMain для всех файлов
            filesTab.tableData.rows.forEach((row: any) => {
              if ('isMain' in row) {
                row.isMain = false;
                if (row.rowId && 'isMain' in row.rowId) {
                  row.rowId.isMain = false;
                }
              }
            });

            // Устанавливаем основным второй файл
            const secondFile = filesTab.tableData.rows[1];
            if (secondFile && 'isMain' in secondFile) {
              secondFile.isMain = true;
              if (secondFile.rowId && 'isMain' in secondFile.rowId) {
                secondFile.rowId.isMain = true;
              }
              mockData.rows[0].rowId.haveMainFile = true;
              internalNoticesState.lastMainFileId = secondFile.rowId?.id || null;
            }
          } else {
            // Нечетный запрос - делаем основным первый файл
            filesTab.tableData.rows.forEach((row: any) => {
              if ('isMain' in row) {
                row.isMain = false;
                if (row.rowId && 'isMain' in row.rowId) {
                  row.rowId.isMain = false;
                }
              }
            });

            const firstFile = filesTab.tableData.rows[0];
            if (firstFile && 'isMain' in firstFile) {
              firstFile.isMain = true;
              if (firstFile.rowId && 'isMain' in firstFile.rowId) {
                firstFile.rowId.isMain = true;
              }
              mockData.rows[0].rowId.haveMainFile = true;
              internalNoticesState.lastMainFileId = firstFile.rowId?.id || null;
            }
          }
        }
      }

      console.log('krg3_internal_notices mock data:', {
        pageNumber,
        pageSize,
        requestCount: internalNoticesState.requestCount,
        lastMainFileId: internalNoticesState.lastMainFileId,
        totalRows: mockData.rows.length,
        totalInPagination: mockData.pagination.total,
      });

      return HttpResponse.json(mockData, { status: 200 });
    },
  ),
];

/** KRG3 nested table handlers - individual tab content loading */
const krg3NestedHandlers = [
  // Package files tab content (lazy loading)
  http.get(
    'http://127.0.0.1:18080/kzid_rest/krg3_package_files_def',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = parseInt(
        url.searchParams.get('pageNumber') || '1',
        10,
      );
      const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);
      const packageId = url.searchParams.get('packageId');

      console.log('Loading package files tab:', { pageNumber, packageId });
      await delay(DEFAULT_DELAY);

      const startIndex = (pageNumber - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const pageFiles = packageFilesData.slice(startIndex, endIndex);

      return HttpResponse.json(
        {
          columns: packageFilesColumns,
          rows: pageFiles,
          pagination: {
            total: packageFilesData.length,
            pageSize,
          },
          pageNumber,
          sort: null,
          group: null,
        },
        { status: 200 },
      );
    },
  ),

  // Request items tab content (lazy loading)
  http.get(
    'http://127.0.0.1:18080/kzid_rest/krg3_request_item',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = parseInt(
        url.searchParams.get('pageNumber') || '1',
        10,
      );
      const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

      await delay(600);

      const startIndex = (pageNumber - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const pageItems = requestItemsData.slice(startIndex, endIndex);

      return HttpResponse.json(
        {
          columns: requestItemsColumns,
          rows: pageItems,
          pagination: {
            total: requestItemsData.length,
            pageSize,
          },
          pageNumber,
          sort: null,
          group: null,
        },
        { status: 200 },
      );
    },
  ),

  // Notice for request tab content (lazy loading)
  http.post(
    'http://127.0.0.1:18080/kzid_rest/krg3_notice_for_request',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = parseInt(
        url.searchParams.get('pageNumber') || '1',
        10,
      );
      const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

      await delay(700);

      const mockData = krg3NoticeForRequestMockData(
        Number(pageNumber),
        pageSize,
      );
      console.log('krg3_notice_for_request mock data:', {
        pageNumber,
        pageSize,
        totalRows: mockData.rows.length,
        totalInPagination: mockData.pagination.total,
        paginationPageSize: mockData.pagination.pageSize,
      });

      return HttpResponse.json(mockData, { status: 200 });
    },
  ),
];

/** KRG3 legacy batch loading handlers - loads all tabs at once (old mode) */
const krg3LegacyHandlers = [
  // Legacy: Load all tabs at once when row is expanded (old lazy mode)
  http.post(
    'http://127.0.0.1:18080/kzid_rest/krg3_request_notice/tabs',
    async ({ request }) => {
      const url = new URL(request.url);
      const requestId = url.searchParams.get('requestNoticeId');

      await delay(800);
      console.log('Loading tabs for requestNoticeId:', requestId);

      // Return structure as in old mode - array of tabs directly
      return HttpResponse.json(
        {
          tabs: [
            {
              label: 'Пункты заявки',
              key: '1',
              endpoint: 'krg3_request_item',
              tableData: {
                columns: requestItemsColumns,
                rows: requestItemsData.slice(0, 5),
                pagination: {
                  total: requestItemsData.length,
                  pageSize: 10,
                },
                pageNumber: 1,
              },
            },
            {
              label: 'Уведомления, связанные с заявкой',
              key: '4',
              endpoint: 'krg3_notice_for_request',
              tableData: {
                columns: [
                  {
                    title: 'Номер уведомления',
                    dataIndex: 'noticeNumber',
                    key: 'noticeNumber',
                    columnType: 'String',
                    width: 150,
                  },
                  {
                    title: 'Дата создания',
                    dataIndex: 'dateCreate',
                    key: 'dateCreate',
                    columnType: 'String',
                    width: 150,
                  },
                  {
                    title: 'Статус',
                    dataIndex: 'status',
                    key: 'status',
                    columnType: 'String',
                    width: 150,
                  },
                ],
                rows: [
                  {
                    key: 'notice-1',
                    noticeNumber: 'УВД-001',
                    dateCreate: '15.11.2022 10:30:00',
                    status: 'Отправлено',
                    rowId: { id: 'notice-1' },
                  },
                  {
                    key: 'notice-2',
                    noticeNumber: 'УВД-002',
                    dateCreate: '16.11.2022 14:20:00',
                    status: 'В работе',
                    rowId: { id: 'notice-2' },
                  },
                ],
                pagination: {
                  total: 2,
                  pageSize: 10,
                },
                pageNumber: 1,
              },
            },
          ],
        },
        { status: 200 },
      );
    },
  ),
];

// ============================================================================
// EPP TREE HANDLERS - Tree structure endpoints
// ============================================================================

/** EPP Tree handlers - Tree structure and file visibility endpoints */
catalogFilesRegistry.set('47ff68f5-896a-4fb5-a777-bc1602fe09b4', 109);
const eppTreeHandlers = [
  // Новый эндпоинт с пагинацией
  http.get(
    'http://127.0.0.1:18080/kzid_rest/krg/epc-tree-page',
    async ({ request }) => {
      const url = new URL(request.url);
      const itemId = url.searchParams.get('itemId');
      const pageNumber = parseInt(
        url.searchParams.get('pageNumber') || '0',
        10,
      );
      const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

      await delay();

      // Корневой каталог
      if (itemId === '47ff68f5-896a-4fb5-a777-bc1602fe09b4') {
        const allItems = [
          {
            title: 'Уведомление ВП 2',
            rawTitle: 'Уведомление ВП 2',
            key: '0-0',
            color: 'rgba(0, 0, 0, 0.65)',
            isFixed: 2,
            itemId: '06890b99-257a-4e37-9690-429293d3a0ed',
            parent: '47ff68f5-896a-4fb5-a777-bc1602fe09b4',
            position: 0,
            disabled: false,
            checked: false,
            cabinetOnly: true,
            tagged: false,
            marked: '',
            linked: [],
            children: [],

            hasLinked: false,
            isLeaf: false,
            isDirectory: true,
            isCallable: false,
          },
          {
            title: 'kzid_kzidfx_429b4ce_5.xlsx',
            rawTitle: 'kzid_kzidfx_429b4ce_5.xlsx',
            key: '0-1',
            color: 'rgba(0,0,255,0.5)',
            isMain: 0,
            itemId: '395cd1e1-9574-400f-9c46-aa8b4b9d9e9b',
            parent: '47ff68f5-896a-4fb5-a777-bc1602fe09b4',
            position: 1,
            disabled: false,
            checked: false,
            cabinetOnly: false,
            tagged: false,
            marked: '',
            fileNetId: '395cd1e1-9574-400f-9c46-aa8b4b9d9e9b',
            fileSignExist: false,
            linked: [],
            children: [],
            isLeaf: true,
            isDirectory: false,
            isCallable: false,
            permissions: {
              canView: true,
              canDownload: true,
              canPrint: true,
            },
            isRemovable: true,
          },
          {
            title: 'qwe.jpg',
            rawTitle: 'qwe.jpg',
            key: '0-2',
            color: 'rgba(0,0,255,0.5)',
            isMain: 0,
            itemId: '241b8afe-7827-4424-9c7e-b290349cdf69',
            parent: '47ff68f5-896a-4fb5-a777-bc1602fe09b4',
            position: 2,
            disabled: false,
            checked: false,
            cabinetOnly: false,
            tagged: false,
            marked: '',
            fileNetId: '241b8afe-7827-4424-9c7e-b290349cdf69',
            fileSignExist: false,
            linked: [],
            children: [],
            isLeaf: true,
            isDirectory: false,
            isCallable: false,
            permissions: {
              canView: true,
              canDownload: true,
              canPrint: true,
            },
            isRemovable: true,
          },
          {
            title: 'ИЗИО',
            rawTitle: 'ИЗИО',
            key: '0-3',
            color: 'rgba(0, 0, 0, 0.65)',
            isFixed: 0,
            itemId: '05753192-1c7a-4e59-b317-20ccfbac01e1',
            parent: '47ff68f5-896a-4fb5-a777-bc1602fe09b4',
            position: 3,
            disabled: false,
            checked: false,
            cabinetOnly: true,
            tagged: false,
            marked: '',
            linked: [],
            children: [],

            hasLinked: false,
            isLeaf: false,
            isDirectory: true,
            isCallable: false,
          },
          // Добавим еще элементов для демонстрации пагинации
          ...Array.from({ length: 18 }, (_, i) => ({
            title: `Файл ${i + 5}.pdf`,
            rawTitle: `Файл ${i + 5}.pdf`,
            key: `0-${i + 4}`,
            color: 'rgba(0,0,255,0.5)',
            isMain: 0,
            itemId: generateUUID(),
            parent: '47ff68f5-896a-4fb5-a777-bc1602fe09b4',
            position: i + 4,
            disabled: false,
            checked: false,
            cabinetOnly: false,
            tagged: false,
            marked: '',
            fileNetId: generateUUID(),
            fileSignExist: false,
            linked: [],
            children: [],
            isLeaf: true,
            isDirectory: false,
            isCallable: false,
            permissions: {
              canView: true,
              canDownload: true,
              canPrint: true,
            },
            isRemovable: true,
          })),
        ];

        // Пагинация
        const startIndex = pageNumber * pageSize;
        const endIndex = startIndex + pageSize;
        const pageData = allItems.slice(startIndex, endIndex);

        return HttpResponse.json(
          {
            treeData: pageData,
            history: [],
            foundNodes: [],
            pagination: {
              total: allItems.length,
              pageNumber,
              pageSize,
            },

          },
          { status: 200 },
        );
      }

      // Для других каталогов возвращаем пустой ответ или генерируем данные
      return HttpResponse.json(
        {
          treeData: [],
          history: [],
          foundNodes: [],
          pagination: {
            total: 0,
            pageNumber,
            pageSize,
          },
        },
        { status: 200 },
      );
    },
  ),

  // Старый эндпоинт без пагинации (для обратной совместимости)
  http.get(
    'http://127.0.0.1:18080/kzid_rest/krg/epc-tree',
    async ({ request }) => {
      const url = new URL(request.url);
      const itemId = url.searchParams.get('itemId');
      const maxDepth = parseInt(url.searchParams.get('maxDepth') || '3', 10);
      const itemsCount = parseInt(
        url.searchParams.get('itemsCount') || '10',
        10,
      );
      const scenario =
        (url.searchParams.get('scenario') as
          | 'mixed'
          | 'files-only'
          | 'dirs-only'
          | 'deep-nested') || 'mixed';

      await delay();

      // Если нет itemId, возвращаем корневые данные
      if (!itemId) {
        return HttpResponse.json(krgTreeRootMockData, { status: 200 });
      }

      // // Стандартные данные для известного itemId
      // if (itemId === '47ff68f5-896a-4fb5-a777-bc1602fe09b4') {
      //   return HttpResponse.json(eppTreeMockData, { status: 200 });
      // }

      // Для любых других itemId автоматически генерируем случайные каталоги

      // Проверяем, есть ли информация о том, сколько файлов должно быть в этом каталоге
      const expectedTotalFiles = catalogFilesRegistry.get(itemId);

      // Если ожидается 0 файлов, нужно проверить, является ли каталог листовым
      if (expectedTotalFiles === 0) {
        // Проверяем в существующих данных, является ли этот каталог листовым
        const existingData = eppTreeMockData.treeData.find(
          (item) => item.itemId === itemId,
        );
        if (existingData?.isLeaf) {
          console.log(
            '📁 Каталог является пустым листом (isLeaf=true), возвращаем пустой результат',
          );
          return HttpResponse.json(
            {
              treeData: [],
              history: [],
              foundNodes: [],
            },
            { status: 200 },
          );
        } else {
          console.log(
            '📁 Каталог пустой, но не лист - может содержать пустые подкаталоги',
          );
        }
      }

      console.log('🎲 Генерируем случайные каталоги для itemId:', {
        itemId,
        maxDepth,
        itemsCount,
        scenario,
        expectedTotalFiles:
          expectedTotalFiles !== undefined
            ? expectedTotalFiles
            : 'не указано, генерируем случайно',
      });

      // Используем расширенный генератор с поддержкой сценариев
      const randomData = generateCatalogByScenario(
        scenario,
        itemId,
        itemsCount,
        expectedTotalFiles,
      );

      const { debug } = randomData;

      // Логируем статистику сгенерированных данных
      const directories = randomData.treeData.filter(
        (item) => item.isDirectory,
      );
      const files = randomData.treeData.filter((item) => !item.isDirectory);
      const totalFilesInDirectories = directories.reduce(
        (sum, dir) => sum + (catalogFilesRegistry.get(dir.itemId!) || 0),
        0,
      );

      const stats = {
        totalItems: randomData.treeData.length,
        directories: directories.length,
        files: files.length,
        totalFilesInDirectories,
        uniqueItemIds: new Set(randomData.treeData.map((item) => item.itemId))
          .size,
      };

      console.log('📊 Статистика сгенерированных данных:', stats);

      if (expectedTotalFiles !== undefined) {
        const leafDirectories = directories.filter((dir) => dir.isLeaf);
        const nonLeafEmptyDirectories = directories.filter(
          (dir) => !dir.isLeaf && (catalogFilesRegistry.get(dir.itemId!) || 0) === 0,
        );

        console.log('✅ ПРОВЕРКА СООТВЕТСТВИЯ:');
        console.log(`   Ожидалось файлов: ${expectedTotalFiles}`);
        console.log(
          `   Прямых файлов: ${files.length} (планировалось: ${
            debug?.actualDirectFilesCount || 'не определено'
          })`,
        );
        console.log(
          `   Файлов в директориях: ${totalFilesInDirectories} (планировалось: ${
            debug?.filesForDirectories || 'не определено'
          })`,
        );
        console.log(
          `   Директорий создано: ${directories.length} (планировалось: ${
            debug?.plannedDirectoryCount || 'не определено'
          })`,
        );
        console.log(
          `   Фактически создано файлов: ${
            debug?.actualFileCount || 'не определено'
          }, директорий: ${debug?.actualDirectoryCount || 'не определено'}`,
        );
        console.log(
          `   Итого файлов: ${files.length + totalFilesInDirectories}`,
        );
        console.log(
          `   Соответствие: ${
            files.length + totalFilesInDirectories === expectedTotalFiles
              ? '✅ ДА'
              : '❌ НЕТ'
          }`,
        );

        if (expectedTotalFiles === 0) {
          console.log('📁 ПУСТЫЕ КАТАЛОГИ:');
          console.log(
            `   Листовые каталоги (isLeaf=true): ${leafDirectories.length}`,
          );
          console.log(
            `   Пустые не-листовые каталоги: ${nonLeafEmptyDirectories.length}`,
          );
        }
      }

      console.log(
        '� Директории с количеством файлов:',
        directories.map((dir) => ({
          title: dir.title,
          filesCount: catalogFilesRegistry.get(dir.itemId!) || 0,
          itemId: dir.itemId,
        })),
      );
      console.log(
        '📄 Примеры файлов:',
        files.slice(0, 3).map((file) => ({
          title: file.title,
          itemId: file.itemId,
        })),
      );

      return HttpResponse.json(randomData, { status: 200 });
    },
  ),

  // File visibility toggle
  http.put(
    'http://127.0.0.1:18080/kzid_rest/krg3_file_data/visibility',
    async () => {
      await delay(0);
      return new HttpResponse(null, { status: 204 });
    },
  ),

  // Mark internal notice file as main (GET request with fileId in URL params)
  http.get(
    'http://127.0.0.1:18080/kzid_rest/krg3_mark_internal_notice_file_main',
    async ({ request }) => {
      const url = new URL(request.url);
      const fileId = url.searchParams.get('fileId');

      if (!fileId) {
        console.error('Missing fileId parameter');
        return new HttpResponse(null, { status: 400 });
      }

      console.log('Marking internal notice file as main:', { fileId });

      // Обновляем состояние
      internalNoticesState.lastMainFileId = fileId;

      // Обновляем данные в моке - помечаем выбранный файл как основной
      const mockData = krg3InternalNoticesMockData;
      if (mockData.rows && mockData.rows[0]?.nestedTable?.tabs) {
        const filesTab = mockData.rows[0].nestedTable.tabs.find((tab: any) => tab.key === '5');
        if (filesTab?.tableData?.rows) {
          // Сбрасываем isMain для всех файлов
          filesTab.tableData.rows.forEach((row: any) => {
            if ('isMain' in row) {
              row.isMain = false;
              if (row.rowId && 'isMain' in row.rowId) {
                row.rowId.isMain = false;
              }
            }
          });

          // Устанавливаем isMain для выбранного файла
          const targetFile = filesTab.tableData.rows.find((row: any) => row.rowId?.id === fileId);
          if (targetFile && 'isMain' in targetFile) {
            targetFile.isMain = true;
            if (targetFile.rowId && 'isMain' in targetFile.rowId) {
              targetFile.rowId.isMain = true;
            }

            // Обновляем haveMainFile в основной строке
            if (mockData.rows[0].rowId) {
              mockData.rows[0].rowId.haveMainFile = true;
            }

            // Обновляем дату последнего изменения
            const now = new Date();
            const formattedDate = now.toLocaleString('ru-RU', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            });
            mockData.rows[0].dateLastChange = formattedDate;

            console.log('Successfully marked file as main:', {
              fileId,
              fileName: targetFile.name,
              isMain: targetFile.isMain
            });
          } else {
            console.warn('File not found:', fileId);
          }
        }
      }

      await delay(300);
      return new HttpResponse(null, { status: 200 });
    },
  ),
];

// ============================================================================
// FILENET HANDLERS - Document management endpoints
// ============================================================================

/** Filenet handlers - Document management and search endpoints */
const filenetHandlers = [
  // Document search
  http.post('http://127.0.0.1:18080/filenet/main/find/documents', async () => {
    await delay(DEFAULT_DELAY);
    return HttpResponse.json(filenetMainFindDocuments, { status: 200 });
  }),

  // Publication details
  http.get('http://127.0.0.1:18080/filenet/publication/get/1226', async () => {
    await delay(DEFAULT_DELAY);
    return HttpResponse.json(filenetPublicationGet, { status: 200 });
  }),

  // Rubrics associations
  http.post(
    'http://127.0.0.1:18080/filenet/publication/audit/associations/rubrics/get',
    async () => {
      await delay(DEFAULT_DELAY);
      return HttpResponse.json(filenetRubrics, { status: 200 });
    },
  ),

  // Associated documents
  http.post(
    'http://127.0.0.1:18080/filenet/publication/audit/associations/assdocs/get',
    async () => {
      await delay(DEFAULT_DELAY);
      return HttpResponse.json(assDocs, { status: 200 });
    },
  ),

  // Rubric tree
  http.get('http://127.0.0.1:18080/filenet/rubric/find/as/tree', async () => {
    await delay(DEFAULT_DELAY);
    return HttpResponse.json(rubricTree, { status: 200 });
  }),
];

// ============================================================================
// UTILITY HANDLERS - System and auxiliary endpoints
// ============================================================================

/** Utility handlers - System status, progress, and other auxiliary endpoints */
const utilityHandlers = [
  // Replicator progress status
  http.get('http://127.0.0.1:18080/kzid_rest/replicator_progress', async () => {
    await delay(DEFAULT_DELAY);
    return HttpResponse.json(
      {
        isLoading: false,
        current: 0,
        total: 0,
        currentFinishDate: '25.12.2024 18:28:09',
        text: null,
      },
      { status: 200 },
    );
  }),

  // KRG4 creation plan
  http.post('http://127.0.0.1:18080/kzid_rest/krg4_creation_plan', async () => {
    await delay(DEFAULT_DELAY);
    return HttpResponse.json(krg4CreationPlan, { status: 200 });
  }),
];

// ============================================================================
// EXPORT ALL HANDLERS
// ============================================================================

/**
 * Combined mock handlers for MSW Organized by functionality for better
 * maintainability
 */
export const handlers = [
  // KRG3 endpoints
  ...krg3MainHandlers,
  ...krg3NestedHandlers,
  ...krg3LegacyHandlers,

  // Tree structure endpoints
  /* ...eppTreeHandlers, */

  // Document management endpoints
  ...filenetHandlers,

  // System and utility endpoints
  ...utilityHandlers,
];
