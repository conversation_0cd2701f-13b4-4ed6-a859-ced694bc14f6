import { Button, Typo<PERSON> } from 'antd';
import { FC, useEffect } from 'react';
import { useToggle } from 'react-use';
import {
  ReplicatorLogStore,
  ReplicatorLogProps,
  ReplicatorLogInnerProps,
} from 'widgets/ReplicatorLog';
import { DataGrid } from 'features/DataGrid';
import { createDownloadModal } from 'features/DownloadModal';
import { specialPermissionsStore } from 'entities/SpecialPermissions';
import { apiUrls } from 'shared/api';
import { generateUrlWithQueryParams } from 'shared/lib';
import {
  useAppDispatch,
  useAppSelector,
  useCreateSliceActions,
} from 'shared/model';
import { AppPopup } from 'shared/ui';

import styles from './styles.module.scss';

const columnWidth = (dataIndex: string): number => {
  switch (dataIndex) {
    case 'number':
      return 200;
    case 'time':
      return 200;
    case 'description':
      return 900;
    default:
      return 200;
  }
};

const ReplicatorLogInner: FC<ReplicatorLogInnerProps> = ({
  log,
  isFullSize,
}) => {
  const dispatch = useAppDispatch();
  const { reset } = useCreateSliceActions(
    ReplicatorLogStore.reducers.slice.actions,
  );

  const logData = useAppSelector(ReplicatorLogStore.selectors.logSelector);

  const handleGetTable = (): void => {
    dispatch(
      ReplicatorLogStore.actions.getTableLogThunk(
        generateUrlWithQueryParams(apiUrls.replicator.log.getDetails, {
          reportId: log.key,
        }),
      ),
    );
  };

  useEffect(() => {
    handleGetTable();

    dispatch(specialPermissionsStore.thunks.getReportPermissionsThunk());
    return reset;
  }, []); // eslint-disable-line

  const reportPermissions = useAppSelector(
    specialPermissionsStore.selectors.reportPermissionsSelector,
  );

  return (
    <>
      <Button
        disabled={!reportPermissions.status.isLoaded}
        type="primary"
        onClick={() =>
          createDownloadModal({
            permissions: reportPermissions.permissions,
            isLazyReport: true,
            title: 'Вывод печатной формы в файл',
            url: apiUrls.replicator.report,
            reportControlDTO: {},
            additionalRequestParams: {
              reportId: log.key as string,
            },
          })
        }
      >
        Печать
      </Button>

      <div className={styles.content}>
        {Array.isArray(log.logInfo) && (
          <div className={styles.contentHeader}>
            {log.logInfo.map((item) => (
              <Typography.Text
                className={styles.contentHeaderText}
                key={item.key}
              >
                {item.title}: <span>{item.description}</span>
              </Typography.Text>
            ))}
          </div>
        )}

        <DataGrid
          additionalClassNames={{
            spinner: styles.contentTableSpinner,
            container: styles.contentTableContainer,
            table: styles.contentTable,
          }}
          columns={logData.table.data.columns}
          rows={logData.table.data.rows}
          columnsProps={(column) => ({ width: columnWidth(column.dataIndex) })}
          tableAdditionProps={{
            loading: logData.table.isPending,
            size: 'small',
            scroll:
              logData.table.data.rows.length >= 25
                ? { y: isFullSize ? '55vh' : 'calc(35vh - 5em)', x: '100%' }
                : { y: isFullSize ? '60vh' : 'calc(37vh - 5em)', x: '100%' },
            pagination: logData.table.data.rows.length >= 25 && {
              pageSize: 25,
              showSizeChanger: false,
            },
          }}
        />
      </div>
    </>
  );
};

export const ReplicatorLog: FC<ReplicatorLogProps> = ({
  date,
  handleClose,
  isOpened,
  ...props
}) => {
  const [isFullSize, toggleFullSize] = useToggle(false);

  return (
    <AppPopup
      isOpened={isOpened}
      fullSizeClassName={styles.containerFull}
      className={styles.container}
      title={`Журнал загрузки на дату ${date}`}
      onClose={handleClose}
      additionalFullSizeHandler={toggleFullSize}
    >
      <ReplicatorLogInner {...props} isFullSize={isFullSize} />
    </AppPopup>
  );
};
