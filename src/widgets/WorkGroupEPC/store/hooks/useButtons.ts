import { useMemo } from 'react';
import {
  EpcPermissions,
  TogglePopup,
  WorkGroupEPCStore,
} from 'widgets/WorkGroupEPC';
import { newlazyTree, TreeActions } from 'features/NewLazyTree';
import { permissionsConfig } from 'entities/Permissions';
// import { apiUrls, appInstance } from 'shared/api';
import { apiUrls, appInstance } from 'shared/api';
import { createConfirmModal, useAppSelector } from 'shared/model';

const TREE_LOADING_TEXT = 'Дождитесь окончания загрузки каталогов';

export const useButtons = ({
  actionOnCheckedNodes,
  resetTree,
  treeKey,
  togglePopup,
  epcPermissions,
}: {
  actionOnCheckedNodes: TreeActions['actionOnCheckedNodes'];
  epcPermissions: EpcPermissions;
  resetTree: TreeActions['resetTree'];
  togglePopup: TogglePopup;
  treeKey: string;
}): AdditionalButton[] => {
  const epc = useAppSelector(WorkGroupEPCStore.selectors.epcSelector);

  const entities = useAppSelector((state) =>
    newlazyTree.selectors.entitiesSelector(state, {
      treeKey,
    }),
  );

  const treeStatuses = useAppSelector((state) =>
    newlazyTree.selectors.treeStatusesSelector(state, {
      treeKey,
    }),
  );
  const treeLoading = treeStatuses.loadingBranch || treeStatuses.isLoading;

  const checkedKeysWithoutRoot = useAppSelector((state) =>
    newlazyTree.selectors.checkedKeysWithoutRootSelector(state, {
      treeKey,
    }),
  );

  const checkedKeys = useAppSelector((state) =>
    newlazyTree.selectors.checkedKeysSelector(state, {
      treeKey,
    }),
  );
  const deletableFilesCount = useMemo(
    () =>
      checkedKeys.reduce<number>(
        (sum, key) =>
          entities[key]?.isRemovable && !entities[key].isDirectory
            ? sum + 1
            : sum,
        0,
      ),
    [checkedKeys, entities],
  );
  const selectedFiles = useAppSelector((state) =>
    newlazyTree.selectors.checkedElementsSelector(state, {
      treeKey,
      filter: (node: TreeElement) => !node.isDirectory,
    }),
  );
  const selectedDirs = useAppSelector((state) =>
    newlazyTree.selectors.checkedElementsSelector(state, {
      treeKey,
      filter: (node: TreeElement) =>
        !!node.isDirectory &&
        !!node.totalCountOfLeafs &&
        node.totalCountOfLeafs > 0,
    }),
  );

  const parsedFiles = useMemo(
    () =>
      selectedFiles.reduce(
        (acc, item) => {
          if (item.isRemovable) {
            acc.deletable.push(item.itemId as Key);
          }

          if (item.permissions?.canDownload) {
            acc.downloadable.push(item.itemId as Key);
          }

          return acc;
        },
        {
          deletable: [],
          downloadable: [],
        } as { deletable: Key[]; downloadable: Key[] },
      ),
    [selectedFiles],
  );

  return [
    {
      title: 'Скачать файлы с описью',
      key: 'download',
      type: 'primary',
      loading: epc.buttons.isPending,
      disabled:
        treeLoading ||
        !epcPermissions.canDownloadFiles ||
        (parsedFiles.downloadable.length === 0 && selectedDirs?.length === 0),
      tooltip: treeLoading
        ? TREE_LOADING_TEXT
        : !epcPermissions.canDownloadFiles
        ? permissionsConfig.warnMessages.noPermissionsDefault('скачивание')
        : parsedFiles.downloadable.length === 0
        ? 'Не выбраны файлы для скачивания'
        : undefined,
      onClick: () => togglePopup('downloadEPC'),
    },
    {
      title: 'Удалить файлы',
      key: 'delete',
      danger: true,
      ghost: true,
      disabled:
        treeLoading ||
        !epcPermissions.canDeleteFiles ||
        deletableFilesCount === 0,
      tooltip: treeLoading
        ? TREE_LOADING_TEXT
        : !epcPermissions.canDeleteFiles
        ? permissionsConfig.warnMessages.noPermissionsDefault('удаление')
        : deletableFilesCount === 0
        ? 'Не выбраны файлы для удаления или нет доступных файлов для удаления'
        : undefined,
      loading: epc.buttons.isPending,
      onClick: () => {
        createConfirmModal({
          title: 'Внимание',
          message: 'Вы действительно хотите удалить файлы?',
          onConfirm: async () => {
            await actionOnCheckedNodes({
              successMessage: 'Файлы успешно удалены',
              filterCallback: (node: TreeElement) =>
                !!node.isRemovable && !node.isDirectory,
              actionCallback: async (nodes: TreeElement[]) => {
                const deleteNodes = nodes.map(({ itemId }) => itemId);
                await appInstance.post(
                  apiUrls.workGroup.fileData.deleteFile,
                  deleteNodes,
                );
              },
              forcePaginationEnabled: true,
            });
          },
        });
      },
    },
    {
      title: 'Доступность элементов в КВ',
      key: 'viewEnable',
      type: 'default',
      disabled:
        treeLoading ||
        !epcPermissions.canEdit ||
        checkedKeysWithoutRoot.length === 0,
      tooltip: treeLoading
        ? TREE_LOADING_TEXT
        : !epcPermissions.canEdit
        ? permissionsConfig.warnMessages.noPermissionsDefault(
            'изменение доступности',
          )
        : checkedKeysWithoutRoot.length === 0
        ? 'Не выбраны элементы для изменения доступности'
        : undefined,
      loading: epc.buttons.isPending,
      onClick: () => {
        togglePopup('visibility');
      },
    },
    {
      title: 'Копировать',
      key: 'copyFiles',
      type: 'default',
      disabled:
        treeLoading ||
        !epcPermissions.canCopyFiles ||
        checkedKeysWithoutRoot.length === 0 ||
        (selectedDirs.length === 0 && selectedFiles.length === 0),
      tooltip: treeLoading
        ? TREE_LOADING_TEXT
        : !epcPermissions.canCopyFiles
        ? permissionsConfig.warnMessages.noPermissionsDefault('копированиe')
        : checkedKeysWithoutRoot.length === 0 ||
          (selectedDirs.length === 0 && selectedFiles.length === 0)
        ? 'Не выбраны элементы, доступные для копирования'
        : undefined,
      loading: epc.buttons.isPending,
      onClick: () => {
        togglePopup('copy');
      },
    },
    {
      title: 'Обновить',
      key: 'refetch',
      type: 'default',
      loading: epc.buttons.isPending,
      onClick: resetTree,
    },
  ];
};
