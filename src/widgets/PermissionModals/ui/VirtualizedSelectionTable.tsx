import { Checkbox } from 'antd';
import React, {
  useMemo,
  useCallback,
  useState,
  useRef,
  useEffect,
} from 'react';
import { FixedSizeList as List } from 'react-window';
import { TableRowData, TableColumnData } from 'features/DataGrid';

interface VirtualizedSelectionTableProps {
  columns: TableColumnData[];
  onSelect: (record: TableRowData, selected: boolean) => void;
  onSelectAll: (
    selected: boolean,
    selectedRows: TableRowData[],
    changeRows: TableRowData[],
  ) => void;
  rows: TableRowData[];
  selectedKeys: Key[];
  height?: number;
  itemHeight?: number;
  overscan?: number;
}

interface VirtualRowProps {
  data: {
    columns: TableColumnData[];
    onSelect: (record: TableRowData, selected: boolean) => void;
    rows: TableRowData[];
    selectedKeys: Set<Key>;
  };
  index: number;
  style: React.CSSProperties;
}

// Мемоизированный компонент строки для предотвращения лишних рендеров
const VirtualRow = React.memo<VirtualRowProps>(({ index, style, data }) => {
  const { rows, columns, selectedKeys, onSelect } = data;
  const row = rows[index];
  const isSelected = selectedKeys.has(row.key);

  const handleCheckboxChange = useCallback(
    (e: { target: { checked: boolean } }) => {
      onSelect(row, e.target.checked);
    },
    [onSelect, row],
  );

  return (
    <div style={style} className="virtual-table-row">
      <div className="virtual-table-row-content">
        <Checkbox checked={isSelected} onChange={handleCheckboxChange} />
        {columns.map((column, colIndex) => (
          <div
            key={column.dataIndex}
            className="virtual-table-cell"
            style={{
              width: column.width || 'auto',
              minWidth: column.width || 100,
            }}
          >
            {column.render
              ? column.render(row[column.dataIndex], row)
              : row[column.dataIndex]}
          </div>
        ))}
      </div>
    </div>
  );
});

VirtualRow.displayName = 'VirtualRow';

/**
 * Виртуализированная таблица с оптимизированным выбором строк Рендерит только
 * видимые строки для улучшения производительности
 */
export const VirtualizedSelectionTable: React.FC<
  VirtualizedSelectionTableProps
> = ({
  rows,
  columns,
  selectedKeys,
  onSelect,
  onSelectAll,
  height = 400,
  itemHeight = 40,
  overscan = 5,
}) => {
  const listRef = useRef<List>(null);
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [isIndeterminate, setIsIndeterminate] = useState(false);

  // Мемоизируем Set для быстрой проверки выбранности
  const selectedKeysSet = useMemo(() => new Set(selectedKeys), [selectedKeys]);

  // Вычисляем состояние "выбрать все"
  useEffect(() => {
    const selectedCount = selectedKeys.length;
    const totalCount = rows.length;

    setIsAllSelected(selectedCount === totalCount && totalCount > 0);
    setIsIndeterminate(selectedCount > 0 && selectedCount < totalCount);
  }, [selectedKeys.length, rows.length]);

  // Обработчик "выбрать все"
  const handleSelectAll = useCallback(
    (e: { target: { checked: boolean } }) => {
      const { checked } = e.target;
      onSelectAll(checked, checked ? rows : [], rows);
    },
    [onSelectAll, rows],
  );

  // Мемоизируем данные для виртуального списка
  const itemData = useMemo(
    () => ({
      rows,
      columns,
      selectedKeys: selectedKeysSet,
      onSelect,
    }),
    [rows, columns, selectedKeysSet, onSelect],
  );

  // Функция для прокрутки к определенной строке
  const scrollToRow = useCallback((index: number) => {
    listRef.current?.scrollToItem(index, 'center');
  }, []);

  return (
    <div className="virtualized-selection-table">
      {/* Заголовок таблицы */}
      <div className="virtual-table-header">
        <div className="virtual-table-header-content">
          <Checkbox
            checked={isAllSelected}
            indeterminate={isIndeterminate}
            onChange={handleSelectAll}
          />
          {columns.map((column) => (
            <div
              key={column.dataIndex}
              className="virtual-table-header-cell"
              style={{
                width: column.width || 'auto',
                minWidth: column.width || 100,
              }}
            >
              {column.title}
            </div>
          ))}
        </div>
      </div>

      {/* Виртуализированный список строк */}
      <List
        ref={listRef}
        height={height}
        itemCount={rows.length}
        itemSize={itemHeight}
        itemData={itemData}
        overscanCount={overscan}
        className="virtual-table-body"
      >
        {VirtualRow}
      </List>

      {/* Информация о выборе */}
      <div className="virtual-table-footer">
        <span>
          Выбрано: {selectedKeys.length} из {rows.length}
        </span>
      </div>
    </div>
  );
};

/** Хук для оптимизированной работы с виртуализированной таблицей */
export const useVirtualizedSelection = (
  rows: TableRowData[],
  initialSelectedKeys: Key[] = [],
) => {
  const [selectedKeys, setSelectedKeys] = useState<Key[]>(initialSelectedKeys);
  const [selectedRows, setSelectedRows] = useState<TableRowData[]>([]);

  // Мемоизируем Map для быстрого поиска строк по ключу
  const rowsMap = useMemo(
    () => new Map(rows.map((row) => [row.key, row])),
    [rows],
  );

  // Синхронизируем selectedRows с selectedKeys
  useEffect(() => {
    const newSelectedRows = selectedKeys
      .map((key) => rowsMap.get(key))
      .filter(Boolean) as TableRowData[];
    setSelectedRows(newSelectedRows);
  }, [selectedKeys, rowsMap]);

  const onSelect = useCallback((record: TableRowData, selected: boolean) => {
    setSelectedKeys((prev) => {
      if (selected) {
        return prev.includes(record.key) ? prev : [...prev, record.key];
      }
      return prev.filter((key) => key !== record.key);
    });
  }, []);

  const onSelectAll = useCallback(
    (
      selected: boolean,
      selectedRows: TableRowData[],
      changeRows: TableRowData[],
    ) => {
      if (selected) {
        setSelectedKeys(rows.map((row) => row.key));
      } else {
        setSelectedKeys([]);
      }
    },
    [rows],
  );

  const clearSelection = useCallback(() => {
    setSelectedKeys([]);
  }, []);

  const selectAll = useCallback(() => {
    setSelectedKeys(rows.map((row) => row.key));
  }, [rows]);

  return {
    selectedKeys,
    selectedRows,
    onSelect,
    onSelectAll,
    clearSelection,
    selectAll,
    isSelected: useCallback(
      (key: Key) => selectedKeys.includes(key),
      [selectedKeys],
    ),
  };
};

// CSS стили для виртуализированной таблицы
export const virtualizedTableStyles = `
.virtualized-selection-table {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.virtual-table-header {
  background: #fafafa;
  border-bottom: 1px solid #d9d9d9;
  padding: 8px 16px;
}

.virtual-table-header-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.virtual-table-header-cell {
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  flex-shrink: 0;
}

.virtual-table-row {
  border-bottom: 1px solid #f0f0f0;
}

.virtual-table-row:hover {
  background-color: #fafafa;
}

.virtual-table-row-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  height: 100%;
}

.virtual-table-cell {
  flex-shrink: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.virtual-table-footer {
  padding: 8px 16px;
  background: #fafafa;
  border-top: 1px solid #d9d9d9;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
}
`;
