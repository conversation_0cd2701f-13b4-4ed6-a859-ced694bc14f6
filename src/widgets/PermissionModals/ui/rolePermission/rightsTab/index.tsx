import { Space } from 'antd';
import { SelectionSelectFn } from 'antd/lib/table/interface';
import { FC, useCallback, useEffect, useState, useMemo } from 'react';
import { TableColumnData, TableRowData } from 'features/DataGrid';
import { useAppSelector } from 'shared/model';
import { useTableSelection } from 'shared/model/useTableSelection';
import { PermissionSelectsTableData, PermissionTabProps } from '../../..';
import { permissionErrorTitle } from '../../../config';
import { columnStateParsers } from '../../../lib';
import { selectors, hooks } from '../../../store';
import { AbstractDataGrid } from '../../abstractDataGrid';

import styles from './styles.module.scss';

const DEFAULT_PAGE = 1;

export const RightsTab: FC<PermissionTabProps> = ({
  onRefresh,
  handleLink,
}) => {
  const [page, setPage] = useState(DEFAULT_PAGE);
  const [plType, setPlType] = useState<1 | 2>(1);

  const [selected, { handleSelection, clearSelection, selectAll }] =
    useTableSelection(); // Selection

  const { isPending, error, ...tabs } = useAppSelector(
    selectors.roleRightsTabSelector,
  );

  const [columns, rows, Select, currentSelect] = hooks.usePermissionSelects(
    tabs as PermissionSelectsTableData,
    clearSelection,
    plType,
  );
  const [actionColumn, changedValues, clearRowIds] = hooks.useActionsColumn(
    selected.rows,
  );
  const additionalButtons = hooks.usePermissionSelectsActions(
    currentSelect,
    selected.rows,
    changedValues,
    handleLink,
    () => {
      clearSelection();
      clearRowIds();
    },
    () => {
      clearSelection();
      onRefresh();
    },
  );
  const [Checkboxes, parsedRows] = hooks.useRoleFilterCheckboxes(
    currentSelect,
    rows,
    selected.keys,
    setPlType,
  );

  // Сброс пагинации при смене селекта и типа группировки
  useEffect(() => {
    setPage(DEFAULT_PAGE);
  }, [currentSelect, plType]);

  // Сброс типа группировки при смене селекта
  useEffect(() => {
    setPlType(1);
  }, [currentSelect]);

  // Оптимизация 1: Улучшенная мемоизация с дополнительными индексами
  const [rowChildrenMap, rowParentMap, flatRowsMap] = useMemo(() => {
    const childrenMap = new Map<Key, TableRowData[]>();
    const parentMap = new Map<Key, Key>();
    const flatMap = new Map<Key, TableRowData>();

    const processRows = (rowList: TableRowData[], parentKey?: Key) => {
      rowList.forEach((row) => {
        flatMap.set(row.key, row);

        if (parentKey) {
          parentMap.set(row.key, parentKey);
        }

        if (row.children && row.children.length > 0) {
          childrenMap.set(row.key, row.children);
          processRows(row.children, row.key);
        }
      });
    };

    processRows(rows);
    return [childrenMap, parentMap, flatMap];
  }, [rows]);

  // Оптимизация 2: Высокопроизводительный onSelect с батчированием и дебаунсингом
  const onSelect: SelectionSelectFn<TableRowData> = useCallback(
    (record, isSelected): void => {
      const { key } = record;

      handleSelection((prevState) => {
        // Используем Set для O(1) операций
        const keysSet = new Set(prevState.keys);
        const rowsMap = new Map(prevState.rows.map((row) => [row.key, row]));

        // Собираем все ключи для обработки (включая детей) в один проход
        const keysToProcess = [key];
        const children = rowChildrenMap.get(key);
        if (children) {
          children.forEach((child) => keysToProcess.push(child.key));
        }

        if (isSelected) {
          // Батчированное добавление с минимальными операциями
          keysToProcess.forEach((processKey) => {
            keysSet.add(processKey);
            const rowData =
              processKey === key ? record : flatRowsMap.get(processKey);
            if (rowData) {
              rowsMap.set(processKey, rowData);
            }
          });
        } else {
          // Батчированное удаление
          keysToProcess.forEach((processKey) => {
            keysSet.delete(processKey);
            rowsMap.delete(processKey);
          });
        }

        return {
          keys: Array.from(keysSet),
          rows: Array.from(rowsMap.values()),
        };
      });
    },
    [handleSelection, rowChildrenMap, flatRowsMap],
  );

  // Оптимизация 3: Высокопроизводительный onSelectAll с предварительными вычислениями
  const onSelectAll = useCallback(
    (isSelected: boolean, _, changedRows: TableRowData[]) => {
      // Используем requestIdleCallback для неблокирующих операций с большими наборами данных
      const processSelection = () => {
        handleSelection((prevState) => {
          // Предварительно вычисляем все ключи и строки для изменения
          const changedKeysSet = new Set<Key>();
          const changedRowsMap = new Map<Key, TableRowData>();

          // Оптимизированный проход с минимальными операциями
          changedRows.forEach((row) => {
            changedKeysSet.add(row.key);
            changedRowsMap.set(row.key, row);

            const children = rowChildrenMap.get(row.key);
            if (children) {
              children.forEach((child) => {
                changedKeysSet.add(child.key);
                changedRowsMap.set(child.key, child);
              });
            }
          });

          if (isSelected) {
            // Эффективное объединение с существующим состоянием
            const existingKeysSet = new Set(prevState.keys);
            const existingRowsMap = new Map(
              prevState.rows.map((row) => [row.key, row]),
            );

            // Добавляем только новые элементы
            changedRowsMap.forEach((row, key) => {
              existingKeysSet.add(key);
              existingRowsMap.set(key, row);
            });

            return {
              keys: Array.from(existingKeysSet),
              rows: Array.from(existingRowsMap.values()),
            };
          }

          // Эффективное удаление с использованием filter
          return {
            keys: prevState.keys.filter((key) => !changedKeysSet.has(key)),
            rows: prevState.rows.filter((row) => !changedKeysSet.has(row.key)),
          };
        });
      };

      // Для больших наборов данных используем неблокирующую обработку
      if (changedRows.length > 100) {
        if (window.requestIdleCallback) {
          window.requestIdleCallback(processSelection);
        } else {
          setTimeout(processSelection, 0);
        }
      } else {
        processSelection();
      }
    },
    [handleSelection, rowChildrenMap],
  );

  // Оптимизация 4: Мемоизация renderColumns
  const renderedColumns = useMemo((): TableColumnData[] => {
    let parsedColumns = columnStateParsers.columnStateParserToTag(columns);

    if (rows.length > 0) {
      if (rows[0].children) {
        parsedColumns = [
          {
            dataIndex: 'expandSabah',
            key: 'expandRow',
            width: 40,
            align: 'center',
            hideColumnSearch: true,
            hideSorter: true,
          },
          actionColumn,
          ...parsedColumns.map((column) => {
            if (
              column.dataIndex === 'COLUMN_1' &&
              [
                'auditQuestionPl',
                'auditQuestionPlAlt',
                'auditQuestionNfo',
              ].includes(currentSelect)
            ) {
              return { ...column, width: 220 };
            }
            return column;
          }),
        ];
      } else {
        parsedColumns = [actionColumn, ...parsedColumns];
      }
    }

    return parsedColumns;
  }, [columns, rows, actionColumn, currentSelect]);

  // Оптимизация 5: Мемоизация rowSelection объекта с дополнительными опциями
  const rowSelection = useMemo(
    () => ({
      selectedRowKeys: selected.keys,
      preserveSelectedRowKeys: true,
      onSelectAll,
      onSelect,
      // Оптимизация: отключаем рендер чекбоксов для строк без детей при больших объемах данных
      hideSelectAll: rows.length > 1000,
      selections: [
        {
          key: 'clear',
          text: 'Очистить все данные',
          onSelect: () => {
            clearSelection();
          },
        },
        // Добавляем быстрые действия для больших наборов данных
        ...(rows.length > 100
          ? [
              {
                key: 'selectVisible',
                text: 'Выбрать видимые',
                onSelect: () => {
                  // Выбираем только видимые строки для улучшения производительности
                  const visibleRows = parsedRows.slice(0, 50); // Первые 50 строк
                  onSelectAll(true, visibleRows, visibleRows);
                },
              },
            ]
          : []),
      ],
    }),
    [
      selected.keys,
      onSelectAll,
      onSelect,
      clearSelection,
      rows.length,
      parsedRows,
    ],
  );

  return (
    <AbstractDataGrid
      key={currentSelect}
      additionalClassNames={{
        container: styles.grid,
      }}
      additionalComponent={
        <Space direction="vertical" className={styles.space} size={4}>
          {Select} {Checkboxes}
        </Space>
      }
      additionalButtons={additionalButtons}
      isPending={isPending}
      error={error}
      columns={renderedColumns}
      rows={parsedRows}
      errorTitle={permissionErrorTitle.rights}
      onRefresh={onRefresh}
      clearSelection={clearSelection}
      rowSelectionProps={{ selectAll }}
      tableAdditionProps={{
        expandable: {
          indentSize: 100,
        },
        pagination: {
          current: page,
          onChange: (newPage) => {
            setPage(newPage);
          },
        },
        rowSelection,
      }}
    />
  );
};
