import { Space } from 'antd';
import { SelectionSelectFn } from 'antd/lib/table/interface';
import { FC, useCallback, useEffect, useState, useMemo } from 'react';
import { TableColumnData, TableRowData } from 'features/DataGrid';
import { useAppSelector } from 'shared/model';
import { useTableSelection } from 'shared/model/useTableSelection';
import { PermissionSelectsTableData, PermissionTabProps } from '../../..';
import { permissionErrorTitle } from '../../../config';
import { columnStateParsers } from '../../../lib';
import { selectors, hooks } from '../../../store';
import { AbstractDataGrid } from '../../abstractDataGrid';

import styles from './styles.module.scss';

const DEFAULT_PAGE = 1;

export const RightsTab: FC<PermissionTabProps> = ({
  onRefresh,
  handleLink,
}) => {
  const [page, setPage] = useState(DEFAULT_PAGE);
  const [plType, setPlType] = useState<1 | 2>(1);

  const [selected, { handleSelection, clearSelection, selectAll }] =
    useTableSelection(); // Selection

  const { isPending, error, ...tabs } = useAppSelector(
    selectors.roleRightsTabSelector,
  );

  const [columns, rows, Select, currentSelect] = hooks.usePermissionSelects(
    tabs as PermissionSelectsTableData,
    clearSelection,
    plType,
  );
  const [actionColumn, changedValues, clearRowIds] = hooks.useActionsColumn(
    selected.rows,
  );
  const additionalButtons = hooks.usePermissionSelectsActions(
    currentSelect,
    selected.rows,
    changedValues,
    handleLink,
    () => {
      clearSelection();
      clearRowIds();
    },
    () => {
      clearSelection();
      onRefresh();
    },
  );
  const [Checkboxes, parsedRows] = hooks.useRoleFilterCheckboxes(
    currentSelect,
    rows,
    selected.keys,
    setPlType,
  );

  // Сброс пагинации при смене селекта и типа группировки
  useEffect(() => {
    setPage(DEFAULT_PAGE);
  }, [currentSelect, plType]);

  // Сброс типа группировки при смене селекта
  useEffect(() => {
    setPlType(1);
  }, [currentSelect]);

  // Оптимизация 1: Мемоизация создания Map для быстрого поиска детей
  const rowChildrenMap = useMemo(() => {
    const map = new Map<Key, TableRowData[]>();
    rows.forEach(row => {
      if (row.children && row.children.length > 0) {
        map.set(row.key, row.children);
      }
    });
    return map;
  }, [rows]);

  // Оптимизация 2: Оптимизированный onSelect с использованием Map и Set
  const onSelect: SelectionSelectFn<TableRowData> = useCallback(
    (record, isSelected): void => {
      const { key } = record;
      
      handleSelection((prevState) => {
        // Используем Set для быстрых операций
        const keysSet = new Set(prevState.keys);
        const rowsMap = new Map(prevState.rows.map(row => [row.key, row]));

        if (isSelected) {
          keysSet.add(key);
          rowsMap.set(key, record);
          
          // Добавляем детей если есть
          const children = rowChildrenMap.get(key);
          if (children) {
            children.forEach(child => {
              keysSet.add(child.key);
              rowsMap.set(child.key, child);
            });
          }
        } else {
          keysSet.delete(key);
          rowsMap.delete(key);
          
          // Удаляем детей если есть
          const children = rowChildrenMap.get(key);
          if (children) {
            children.forEach(child => {
              keysSet.delete(child.key);
              rowsMap.delete(child.key);
            });
          }
        }

        return {
          keys: Array.from(keysSet),
          rows: Array.from(rowsMap.values()),
        };
      });
    },
    [handleSelection, rowChildrenMap],
  );

  // Оптимизация 3: Оптимизированный onSelectAll с батчированием операций
  const onSelectAll = useCallback(
    (isSelected: boolean, _, changedRows: TableRowData[]) => {
      handleSelection((prevState) => {
        // Подготавливаем все измененные строки заранее
        const changedFlatRows: TableRowData[] = [];
        const changedKeysSet = new Set<Key>();
        
        changedRows.forEach(row => {
          changedFlatRows.push(row);
          changedKeysSet.add(row.key);
          
          const children = rowChildrenMap.get(row.key);
          if (children) {
            children.forEach(child => {
              changedFlatRows.push(child);
              changedKeysSet.add(child.key);
            });
          }
        });

        if (isSelected) {
          // Используем Set для объединения без дубликатов
          const newKeysSet = new Set([...prevState.keys, ...changedKeysSet]);
          const rowsMap = new Map(prevState.rows.map(row => [row.key, row]));
          
          changedFlatRows.forEach(row => {
            rowsMap.set(row.key, row);
          });
          
          return {
            keys: Array.from(newKeysSet),
            rows: Array.from(rowsMap.values()),
          };
        }
        // Фильтрация через Set для O(1) проверки
        return {
          keys: prevState.keys.filter((key) => !changedKeysSet.has(key)),
          rows: prevState.rows.filter((row) => !changedKeysSet.has(row.key)),
        };
      });
    },
    [handleSelection, rowChildrenMap],
  );

  // Оптимизация 4: Мемоизация renderColumns
  const renderedColumns = useMemo((): TableColumnData[] => {
    let parsedColumns = columnStateParsers.columnStateParserToTag(columns);

    if (rows.length > 0) {
      if (rows[0].children) {
        parsedColumns = [
          {
            dataIndex: 'expandSabah',
            key: 'expandRow',
            width: 40,
            align: 'center',
            hideColumnSearch: true,
            hideSorter: true,
          },
          actionColumn,
          ...parsedColumns.map((column) => {
            if (
              column.dataIndex === 'COLUMN_1' &&
              [
                'auditQuestionPl',
                'auditQuestionPlAlt',
                'auditQuestionNfo',
              ].includes(currentSelect)
            ) {
              return { ...column, width: 220 };
            }
            return column;
          }),
        ];
      } else {
        parsedColumns = [actionColumn, ...parsedColumns];
      }
    }

    return parsedColumns;
  }, [columns, rows, actionColumn, currentSelect]);

  // Оптимизация 5: Мемоизация rowSelection объекта
  const rowSelection = useMemo(
    () => ({
      selectedRowKeys: selected.keys,
      preserveSelectedRowKeys: true,
      onSelectAll,
      onSelect,
      selections: [
        {
          key: 'clear',
          text: 'Очистить все данные',
          onSelect: () => {
            clearSelection();
          },
        },
      ],
    }),
    [selected.keys, onSelectAll, onSelect, clearSelection]
  );

  return (
    <AbstractDataGrid
      key={currentSelect}
      additionalClassNames={{
        container: styles.grid,
      }}
      additionalComponent={
        <Space direction="vertical" className={styles.space} size={4}>
          {Select} {Checkboxes}
        </Space>
      }
      additionalButtons={additionalButtons}
      isPending={isPending}
      error={error}
      columns={renderedColumns}
      rows={parsedRows}
      errorTitle={permissionErrorTitle.rights}
      onRefresh={onRefresh}
      clearSelection={clearSelection}
      rowSelectionProps={{ selectAll }}
      tableAdditionProps={{
        expandable: {
          indentSize: 100,
        },
        pagination: {
          current: page,
          onChange: (newPage) => {
            setPage(newPage);
          },
        },
        rowSelection,
      }}
    />
  );
};
