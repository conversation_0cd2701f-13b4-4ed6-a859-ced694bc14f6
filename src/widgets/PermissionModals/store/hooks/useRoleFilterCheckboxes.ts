import { Checkbox, Tooltip } from 'antd';
import { CheckboxOptionType } from 'antd/lib/checkbox/Group';
import moment from 'moment';
import { createElement, useEffect, useMemo, useState } from 'react';
import { TableRowData } from 'features/DataGrid';
import { DEFAULT_DATE_VARIANT } from 'shared/config/constants';
import { deleteKeysFromObject } from 'shared/model';
import { PermissionSelect } from '../..';

const CHANGED = 'changed';
const SELECTED = 'selected';
const ACTUAL = 'actual';
const STATUS_COLUMN_NAME = 'COLUMN_1';
const STATUS_NEW_COLUMN_NAME = 'state';
const DATE_COLUMN = 'COLUMN_4';
const ANY_DATE_LABEL = 'Любая дата';
const YEARS_AFTER = 2;

const cbKeys = [CHANGED, SELECTED, ACTUAL] as const;

/* Опции чекбоксов */
const checkboxOptions: Record<
  typeof cbKeys[number],
  CheckboxOptionType & { hint: string }
> = {
  [CHANGED]: {
    label: 'Только измененные права',
    value: CHANGED,
    hint: 'Показывать только права на изменение',
  },
  [SELECTED]: {
    label: 'Только установленные права',
    value: SELECTED,
    hint: 'Показывать только права с указанным состоянием',
  },
  [ACTUAL]: {
    label: 'Только актуальные права',
    value: ACTUAL,
    hint: `Показывать только права с окончанием ${YEARS_AFTER} года назад от текущего года`,
  },
};

export const useRoleFilterCheckboxes = (
  permissionSelect: PermissionSelect,
  rows: TableRowData[],
  selectedKeys: Key[],
  setPlType?: (plType: 1 | 2) => void,
): [typeof checkboxes, TableRowData[]] => {
  // Оптимизация: Мемоизация Set для selectedKeys
  const selectedSet = useMemo(() => new Set(selectedKeys), [selectedKeys]);

  const [checkbox, setCheckbox] = useState<typeof cbKeys[number][]>([]);

  // Оптимизация: Мемоизация доступных опций
  const availableOptions = useMemo(() => {
    const optionsMap: Record<PermissionSelect, typeof cbKeys[number][]> = {
      inspection: [CHANGED, SELECTED],
      okato: [CHANGED, SELECTED],
      regionNfo: [CHANGED, SELECTED],
      regionPl: [CHANGED, SELECTED],
      auditQuestion: [CHANGED, SELECTED, ACTUAL],
      auditQuestionNfo: [CHANGED, SELECTED, ACTUAL],
      auditQuestionPl: [CHANGED, SELECTED, ACTUAL],
      auditQuestionPlAlt: [CHANGED, SELECTED, ACTUAL],
      dateRange: [CHANGED, SELECTED, ACTUAL],
      checkList: [CHANGED, SELECTED],
      documentAction: [CHANGED, SELECTED],
      reportType: [CHANGED, SELECTED],
      wildCardsType: [CHANGED, SELECTED],
    };

    return optionsMap[permissionSelect].map((cb) => checkboxOptions[cb]);
  }, [permissionSelect]);

  useEffect(() => {
    setCheckbox([]);
  }, [permissionSelect]);

  // Оптимизация: Мемоизация компонента checkboxes
  const checkboxes = useMemo(
    () =>
      createElement(
        Checkbox.Group,
        {
          value: checkbox,
          onChange: (value) => setCheckbox(value as typeof checkbox),
        },
        [
          ...availableOptions.map((cb) =>
            createElement(
              Tooltip,
              {
                title: cb.hint,
                key: cb.hint,
                arrowPointAtCenter: true,
                placement: 'topLeft',
              },
              createElement(Checkbox, { value: cb.value }, cb.label),
            ),
          ),
          permissionSelect === 'auditQuestionPl'
            ? createElement(
                Checkbox,
                {
                  key: `${permissionSelect}Alt`,
                  value: true,
                  onChange: (e) =>
                    e.target.checked ? setPlType?.(2) : setPlType?.(1),
                },
                'Альтернативная группировка',
              )
            : [],
        ].flat(1),
      ),
    [checkbox, availableOptions, permissionSelect, setPlType],
  );

  // Оптимизация: Предварительные вычисления для фильтрации по дате
  const twoYearsAgo = useMemo(
    () => moment().subtract(YEARS_AFTER, 'years'),
    [],
  );

  // Оптимизация: Оптимизированная фильтрация с кешированием результатов
  const filteredRows = useMemo(() => {
    if (checkbox.length === 0) {
      return rows;
    }

    // Создаем функции фильтрации заранее для избежания повторных проверок
    const filters: Array<(row: TableRowData) => boolean> = [];

    if (checkbox.includes(CHANGED)) {
      filters.push((row) => selectedSet.has(row.key));
    }

    if (checkbox.includes(SELECTED)) {
      filters.push((row) => {
        if (STATUS_COLUMN_NAME in row) {
          return row[STATUS_COLUMN_NAME] !== null;
        }
        if (STATUS_NEW_COLUMN_NAME in row) {
          return row[STATUS_NEW_COLUMN_NAME] !== null;
        }
        return false;
      });
    }

    if (checkbox.includes(ACTUAL)) {
      filters.push(
        (row) =>
          row[DATE_COLUMN] === ANY_DATE_LABEL ||
          moment(row[DATE_COLUMN], DEFAULT_DATE_VARIANT).isAfter(twoYearsAgo),
      );
    }

    // Оптимизированная рекурсивная фильтрация
    const filterRows = (rowsForFiltering: TableRowData[]): TableRowData[] => {
      const result: TableRowData[] = [];

      rowsForFiltering.forEach((row) => {
        // Проверяем все фильтры для текущей строки
        const passesFilters = filters.every((filter) => filter(row));

        if (row.children && row.children.length > 0) {
          const filteredChildren = filterRows(row.children);

          if (filteredChildren.length > 0) {
            // Если есть отфильтрованные дети, включаем родителя с детьми
            result.push({ ...row, children: filteredChildren });
          } else if (passesFilters) {
            // Если детей нет, но родитель проходит фильтры
            result.push(
              deleteKeysFromObject(row, ['children']) as TableRowData,
            );
          }
        } else if (passesFilters) {
          // Строка без детей, которая проходит фильтры
          result.push(row);
        }
      });

      return result;
    };

    return filterRows(rows);
  }, [checkbox, rows, selectedSet, twoYearsAgo]);

  return [checkboxes, filteredRows];
};
