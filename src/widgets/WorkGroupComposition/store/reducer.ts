import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { TableRowData } from 'features/DataGrid';
import { WorkGroupCompositionInitial } from '..';
import {
  getDataThunk,
  getNestedDataThunk,
  getPermissionsSelectThunk,
  postDataThunk,
} from './actions';

const NESTED_COLUMNS = [
  {
    title: 'Нахождение в других КРГ',
    dataIndex: 'cabinetCode',
    key: '1',
  },
  { title: 'Роль', dataIndex: 'role', key: '2' },
  { title: 'Наименование ПЛ', dataIndex: 'orgName', key: '3' },
  { title: 'Рег. №', dataIndex: 'orgRegNumber', key: '4' },
  { title: 'ИНН', dataIndex: 'orgInn', key: '5' },
  { title: 'ОГРН', dataIndex: 'orgOgrn', key: '6' },
];

export const initialState: WorkGroupCompositionInitial = {
  composition: {
    creation: {
      table: { columns: [], rows: [] },
      isPending: false,
      error: null,
    },
    update: {
      isPending: false,
      error: null,
      select: {
        array: [],
        selectedValue: null,
      },
    },
  },
};

export const slice = createSlice({
  name: 'workGroupComposition',
  initialState,
  reducers: {
    reset: () => ({ ...initialState }),
    handleSelect: (
      state,
      { payload }: PayloadAction<{ index: number; value: string }>,
    ) => {
      const newRow: TableRowData[] = JSON.parse(
        JSON.stringify(state.composition.creation.table.rows),
      );
      newRow[payload.index].permissions = payload.value;
      state.composition.creation.table.rows = newRow;
    },
    handleCheck: (state, { payload }: PayloadAction<number>) => {
      state.composition.creation.table.rows[payload].checked =
        !state.composition.creation.table.rows[payload].checked;
    },
    handleCheckKeys: (state, { payload }: PayloadAction<Key[]>) => {
      const keysSet = new Set();
      payload.forEach((key) => keysSet.add(key));

      state.composition.creation.table.rows.forEach((row, index) => {
        row.checked = keysSet.has(index + 1);
      });
    },
    handleCheckAll: (state, { payload }: PayloadAction<boolean>) => {
      const newRow = JSON.parse(
        JSON.stringify(state.composition.creation.table.rows),
      );

      state.composition.creation.table.rows = newRow.map(
        (row: TableRowData) => ({
          ...row,
          checked: payload,
        }),
      );
    },
    handleSelectPermissions: (state, { payload }: PayloadAction<string>) => {
      state.composition.update.select.selectedValue = payload;
    },
  },
  extraReducers: (builder) => {
    [getDataThunk, postDataThunk].forEach((thunk) => {
      builder.addCase(thunk.pending, (state) => {
        state.composition.creation.error = null;
        state.composition.creation.isPending = true;
      });

      builder.addCase(thunk.rejected, (state, { error }) => {
        state.composition.creation.error = error;
        state.composition.creation.isPending = false;
      });
    });

    builder.addCase(getDataThunk.fulfilled, (state, { payload }) => {
      state.composition.creation.error = null;
      state.composition.creation.isPending = false;

      state.composition.creation.table = {
        columns: payload.columns,
        rows: payload.rows.map((item) => ({
          ...item,
          checked: false,
          checkbox: '' as unknown as undefined,
          permissions: state.composition.update.select.selectedValue,
          nestedTable: {
            columns: NESTED_COLUMNS,
            rows: (item?.nestedTable as TableRowData[]) || [],
          },
        })),
      };
    });

    builder.addCase(getNestedDataThunk.pending, (state, { meta }) => {
      const { userId } = meta.arg;
      const row = state.composition.creation.table.rows.find(
        (item) =>
          'rowId' in item &&
          'memberId' in item.rowId! &&
          item.rowId.memberId === userId,
      );
      if (row) {
        row.nestedTableStatus = {
          isLoading: true,
          isLoaded: false,
          isError: false,
          error: null,
        };
      }
    });

    builder.addCase(getNestedDataThunk.rejected, (state, { meta, error }) => {
      const { userId } = meta.arg;
      const row = state.composition.creation.table.rows.find(
        (item) =>
          'rowId' in item &&
          'memberId' in item.rowId! &&
          item.rowId.memberId === userId,
      );
      if (row) {
        row.nestedTableStatus = {
          isLoading: false,
          isLoaded: false,
          isError: true,
          error,
        };
      }
    });

    builder.addCase(
      getNestedDataThunk.fulfilled,
      (state, { meta, payload }) => {
        const { userId } = meta.arg;
        const row = state.composition.creation.table.rows.find(
          (item) =>
            'rowId' in item &&
            'memberId' in item.rowId! &&
            item.rowId.memberId === userId,
        );
        if (row) {
          row.nestedTable = {
            columns: NESTED_COLUMNS,
            rows: payload,
          };
          row.nestedTableStatus = {
            isLoading: false,
            isLoaded: true,
            isError: false,
            error: null,
          };
        }
      },
    );

    builder.addCase(getPermissionsSelectThunk.pending, (state) => {
      state.composition.update.isPending = true;
      state.composition.update.error = null;
    });

    builder.addCase(getPermissionsSelectThunk.rejected, (state, { error }) => {
      state.composition.update.isPending = false;
      state.composition.update.error = error;
    });

    builder.addCase(
      getPermissionsSelectThunk.fulfilled,
      (state, { payload }) => {
        state.composition.update.isPending = false;
        state.composition.update.select.array = payload;

        if (payload.length !== 0 && payload.some((i) => i.isSelected)) {
          const selectedIndex = payload.findIndex((i) => i.isSelected);
          state.composition.update.select.selectedValue =
            payload[selectedIndex].value;
        }
      },
    );
  },
});
